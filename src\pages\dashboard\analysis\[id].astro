---
export const prerender = false;
import Layout from '../../../layouts/Layout.astro';
import Navbar from '../../../components/dashboard/Navbar.vue';
import AnalysisResultsNew from '../../../components/analysis/AnalysisResultsNew.vue';
import FullScreenProgressTracker from '../../../components/analysis/FullScreenProgressTracker.vue';
import { supabaseAdmin } from '../../../lib/supabase-client';

const { id } = Astro.params;

if (!id) {
  return Astro.redirect('/dashboard');
}

// Fetch analysis data to check status
const { data: analysis, error } = await supabaseAdmin
  .from('analyses')
  .select('id, url, title, generation_status')
  .eq('id', id)
  .single();

if (error || !analysis) {
  return Astro.redirect('/dashboard');
}

// Check if analysis is still in progress
const isInProgress = analysis.generation_status === 'pending' || analysis.generation_status === 'in_progress';
---

<Layout title={`Analysis: ${analysis.title} - ConvertIQ`} isAuthenticated={true}>
  <!-- Full-screen progress tracker (shown during analysis) -->
  {isInProgress && (
    <FullScreenProgressTracker
      client:load
      analysisId={id}
      websiteUrl={analysis.url}
    />
  )}

  <!-- Normal dashboard layout (shown after completion or if not in progress) -->
  <div
    class="flex-1 flex flex-col overflow-hidden transition-opacity duration-500"
    id="dashboard-content"
  >
    <Navbar client:load />
    <main class="flex-1 overflow-y-auto bg-gray-50">
      <AnalysisResultsNew client:load analysisId={id} />
    </main>
  </div>

  <script>
  document.addEventListener('DOMContentLoaded', () => {
    const progressTrackerContainer = document.getElementById('progress-tracker-container');
    const dashboardContent = document.getElementById('dashboard-content');
    let isAnalysisComplete = false;

    const showDashboard = () => {
      if (progressTrackerContainer) {
        progressTrackerContainer.style.transition = 'opacity 0.5s ease-out';
        progressTrackerContainer.style.opacity = '0';
        setTimeout(() => {
          progressTrackerContainer.style.display = 'none';
        }, 500);
      }

      if (dashboardContent) {
        dashboardContent.style.display = 'block';
        dashboardContent.style.opacity = '0';
        setTimeout(() => {
          dashboardContent.style.transition = 'opacity 0.5s ease-in';
          dashboardContent.style.opacity = '1';
        }, 100); // Delay slightly to ensure it fades in after the tracker starts fading out
      }
    };

    // Listen for analysis completion from the progress tracker
    document.addEventListener('analysis-complete', (event) => {
      console.log('Analysis complete event received', event.detail);
      isAnalysisComplete = true;
      // The FullScreenProgressTracker will handle its own fade-out.
      // We'll just show the dashboard content.
      showDashboard();
    });

    // This event is fired by the progress tracker after it has faded out.
    document.addEventListener('all-progress-hidden', () => {
      console.log('All progress hidden event received');
      if (isAnalysisComplete) {
        showDashboard();
      }
    });

    // Initial check in case the page loads after analysis is already complete
    const isAlreadyComplete = progressTrackerContainer?.dataset.isComplete === 'true';
    if (isAlreadyComplete) {
       console.log('Analysis is already complete on page load.');
       // Hide progress tracker immediately and show dashboard
       if (progressTrackerContainer) progressTrackerContainer.style.display = 'none';
       if (dashboardContent) {
        dashboardContent.style.display = 'block';
        dashboardContent.style.opacity = '1';
       }
    }
  });
</script>
</Layout>