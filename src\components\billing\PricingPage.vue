<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { createCheckoutSession, redirectToCheckout } from '../../lib/stripe-client';
import { BillingService } from '../../lib/billing-service';
import type { AnalysisPackage } from '../../types/billing';
import { 
  ArrowLeft, 
  Check, 
  Package, 
  Zap, 
  Crown, 
  MessageCircle, 
  RefreshCw,
  ShoppingCart,
  Plus,
  Minus
} from 'lucide-vue-next';

const packages = ref<AnalysisPackage[]>([]);
const quantities = ref<Record<string, number>>({});
const purchasing = ref<string | null>(null);
const loading = ref(true);

onMounted(async () => {
  await loadPackages();
});

const loadPackages = async () => {
  try {
    packages.value = await BillingService.getAvailablePackages();
    
    // Initialize quantities
    packages.value.forEach(pkg => {
      quantities.value[pkg.id] = 1;
    });
  } catch (error) {
    console.error('Error loading packages:', error);
  } finally {
    loading.value = false;
  }
};

const updateQuantity = (packageId: string, change: number) => {
  const current = quantities.value[packageId] || 1;
  const newQuantity = Math.max(1, Math.min(10, current + change));
  quantities.value[packageId] = newQuantity;
};

const purchasePackage = async (packageId: string) => {
  try {
    purchasing.value = packageId;
    
    const { sessionId } = await createCheckoutSession({
      productId: packageId,
      quantity: quantities.value[packageId] || 1,
      successUrl: `${window.location.origin}/dashboard/billing?success=true`,
      cancelUrl: `${window.location.origin}/pricing?canceled=true`
    });

    await redirectToCheckout(sessionId);

  } catch (error) {
    console.error('Error purchasing package:', error);
  } finally {
    purchasing.value = null;
  }
};

const getPackageIcon = (packageId: string) => {
  switch (packageId) {
    case 'basic': return Package;
    case 'professional': return Zap;
    case 'premium': return Crown;
    default: return Package;
  }
};

const getPackageColor = (packageId: string) => {
  switch (packageId) {
    case 'basic': return 'border-blue-200';
    case 'professional': return 'border-purple-500 ring-2 ring-purple-100';
    case 'premium': return 'border-yellow-200';
    default: return 'border-gray-200';
  }
};

const getTotalPrice = (pkg: AnalysisPackage) => {
  return (pkg.price * (quantities.value[pkg.id] || 1)).toFixed(2);
};
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header with back navigation -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex items-center space-x-4">
          <a href="/dashboard" class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors">
            <ArrowLeft class="w-4 h-4" />
            <span>Back to Dashboard</span>
          </a>
        </div>
      </div>
    </div>

    <!-- Main pricing content -->
    <div class="max-w-7xl mx-auto px-6 py-16">
      <!-- Header -->
      <div class="text-center mb-16">
        <h1 class="text-4xl font-bold text-gray-900 mb-6">Analysis Packages</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Choose the perfect analysis package for your needs. All packages include credits that never expire.
        </p>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div v-for="i in 3" :key="i" class="bg-white rounded-lg border border-gray-200 p-8">
          <div class="animate-pulse space-y-4">
            <div class="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
            <div class="h-8 bg-gray-200 rounded w-1/3 mx-auto"></div>
            <div class="space-y-2">
              <div class="h-3 bg-gray-200 rounded"></div>
              <div class="h-3 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pricing cards -->
      <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <div 
          v-for="pkg in packages" 
          :key="pkg.id"
          class="bg-white rounded-lg border-2 p-8 relative transition-all duration-200 hover:shadow-lg"
          :class="getPackageColor(pkg.id)"
        >
          <!-- Popular Badge -->
          <div v-if="pkg.id === 'professional'" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-purple-500 text-white px-4 py-1 rounded-full text-sm font-medium">
              Most Popular
            </span>
          </div>

          <div class="text-center mb-8">
            <div class="w-12 h-12 mx-auto mb-4 rounded-xl flex items-center justify-center"
                 :class="pkg.id === 'basic' ? 'bg-blue-100' : pkg.id === 'professional' ? 'bg-purple-100' : 'bg-yellow-100'">
              <component 
                :is="getPackageIcon(pkg.id)" 
                class="w-6 h-6"
                :class="pkg.id === 'basic' ? 'text-blue-600' : pkg.id === 'professional' ? 'text-purple-600' : 'text-yellow-600'"
              />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ pkg.name }}</h3>
            <p class="text-gray-600 mb-6">{{ pkg.description }}</p>
            
            <!-- Quantity Selector -->
            <div class="flex items-center justify-center space-x-3 mb-4">
              <button
                @click="updateQuantity(pkg.id, -1)"
                :disabled="quantities[pkg.id] <= 1"
                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Minus class="w-4 h-4" />
              </button>
              <span class="text-lg font-medium w-8 text-center">{{ quantities[pkg.id] || 1 }}</span>
              <button
                @click="updateQuantity(pkg.id, 1)"
                :disabled="quantities[pkg.id] >= 10"
                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus class="w-4 h-4" />
              </button>
            </div>
            
            <div class="mb-6">
              <span class="text-4xl font-bold text-gray-900">${{ getTotalPrice(pkg) }}</span>
              <span v-if="quantities[pkg.id] > 1" class="text-gray-600 block text-sm">
                ${{ pkg.price }} × {{ quantities[pkg.id] }}
              </span>
            </div>
          </div>

          <!-- Features -->
          <ul class="space-y-3 mb-8">
            <li class="flex items-center space-x-3">
              <Check class="w-4 h-4 text-green-500" />
              <span class="text-gray-700 text-sm">Page Summary & Screenshot</span>
            </li>
            <li class="flex items-center space-x-3">
              <Check class="w-4 h-4 text-green-500" />
              <span class="text-gray-700 text-sm">Conversion Score Analysis</span>
            </li>
            <li class="flex items-center space-x-3" :class="pkg.features.has_performance ? '' : 'opacity-50'">
              <Check v-if="pkg.features.has_performance" class="w-4 h-4 text-green-500" />
              <div v-else class="w-4 h-4 border border-gray-300 rounded"></div>
              <span class="text-gray-700 text-sm">Performance Analysis</span>
            </li>
            <li class="flex items-center space-x-3" :class="pkg.features.has_suggestions ? '' : 'opacity-50'">
              <Check v-if="pkg.features.has_suggestions" class="w-4 h-4 text-green-500" />
              <div v-else class="w-4 h-4 border border-gray-300 rounded"></div>
              <span class="text-gray-700 text-sm">AI Suggestions</span>
            </li>
            <li class="flex items-center space-x-3" :class="pkg.features.has_leadinsights ? '' : 'opacity-50'">
              <Check v-if="pkg.features.has_leadinsights" class="w-4 h-4 text-green-500" />
              <div v-else class="w-4 h-4 border border-gray-300 rounded"></div>
              <span class="text-gray-700 text-sm">Lead Insights</span>
            </li>
            <li class="flex items-center space-x-3">
              <MessageCircle class="w-4 h-4 text-blue-500" />
              <span class="text-gray-700 text-sm">{{ pkg.features.chat_messages * (quantities[pkg.id] || 1) }} Chat Messages</span>
            </li>
            <li class="flex items-center space-x-3">
              <RefreshCw class="w-4 h-4 text-purple-500" />
              <span class="text-gray-700 text-sm">{{ pkg.features.reruns * (quantities[pkg.id] || 1) }} Reruns</span>
            </li>
          </ul>

          <!-- Purchase Button -->
          <button
            @click="purchasePackage(pkg.id)"
            :disabled="purchasing === pkg.id"
            class="w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            :class="pkg.id === 'professional' 
              ? 'bg-purple-600 text-white hover:bg-purple-700 shadow-lg hover:shadow-xl' 
              : 'bg-blue-600 text-white hover:bg-blue-700'"
          >
            <div v-if="purchasing === pkg.id" class="flex items-center justify-center">
              <Loader2 class="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </div>
            <div v-else class="flex items-center justify-center">
              <ShoppingCart class="w-4 h-4 mr-2" />
              Purchase Package
            </div>
          </button>
        </div>
      </div>

      <!-- Add-ons Section -->
      <div class="mt-16 text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Need More Credits?</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="text-center mb-4">
              <MessageCircle class="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <h3 class="text-lg font-bold text-gray-900">Extra Chat Messages</h3>
              <p class="text-gray-600 text-sm">Add 25 additional chat messages</p>
            </div>
            <div class="text-2xl font-bold text-gray-900 mb-4">$4.99</div>
            <button
              @click="purchasePackage('chat_addon')"
              :disabled="purchasing === 'chat_addon'"
              class="w-full btn-secondary"
            >
              <ShoppingCart class="w-4 h-4 mr-2" />
              Purchase Add-on
            </button>
          </div>

          <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="text-center mb-4">
              <RefreshCw class="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <h3 class="text-lg font-bold text-gray-900">Extra Reruns</h3>
              <p class="text-gray-600 text-sm">Add 3 additional analysis reruns</p>
            </div>
            <div class="text-2xl font-bold text-gray-900 mb-4">$7.99</div>
            <button
              @click="purchasePackage('rerun_addon')"
              :disabled="purchasing === 'rerun_addon'"
              class="w-full btn-secondary"
            >
              <ShoppingCart class="w-4 h-4 mr-2" />
              Purchase Add-on
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.btn-secondary {
  @apply inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}
</style>