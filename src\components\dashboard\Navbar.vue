<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { supabase } from '../../lib/supabase';
import { LayoutDashboard, CreditCard, MessageCircle, Plus, CircleUser, LogOut, History, X, } from 'lucide-vue-next';

const user = ref<any>(null);
const profileMenuOpen = ref(false);
const loading = ref(true);
const currentPath = ref('');
const isClient = ref(false);

const handleClickOutside = (event: Event) => {
  const target = event.target as Element;
  if (profileMenuOpen.value && !target.closest('.profile-menu-container')) {
    profileMenuOpen.value = false;
  }
};

onMounted(async () => {
  isClient.value = true;
  try {
    // Set current path on client side only
    currentPath.value = window.location.pathname;

    const { data } = await supabase.auth.getUser();
    if (data?.user) {
      user.value = data.user;

      // Get profile data
      const { data: profileData } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();

      if (profileData) {
        user.value = {
          ...user.value,
          ...profileData
        };

        // If the avatar URL is from Google, cache it in Supabase Storage
        if (user.value.avatar_url && user.value.avatar_url.includes('googleusercontent.com') && user.value.id) {
          try {
            const { data: sessionData } = await supabase.auth.getSession();
            const accessToken = sessionData.session?.access_token;

            if (accessToken) {
              const response = await fetch('/api/cache-avatar', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  avatarUrl: user.value.avatar_url,
                  userId: user.value.id,
                  accessToken: accessToken
                })
              });
              if (response.ok) {
                const data = await response.json();
                if (data.newAvatarUrl) {
                  user.value.avatar_url = data.newAvatarUrl; // Update to cached URL
                } else {
                  console.error('Failed to cache avatar: No newAvatarUrl in response');
                }
              } else {
                const errorText = await response.text(); // Read error response as text
                console.error('Failed to cache avatar:', response.status, errorText);
              }
            }
          } catch (cacheError) {
            console.error('Error calling cache-avatar API:', cacheError);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error loading user:', error);
  } finally {
    loading.value = false;
  }
  
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    document.removeEventListener('click', handleClickOutside);
  }
});

const toggleProfileMenu = () => {
  profileMenuOpen.value = !profileMenuOpen.value;
};

const signOut = async () => {
  try {
    await supabase.auth.signOut();
    window.location.href = '/';
  } catch (error) {
    console.error('Error signing out:', error);
  }
};

const isCurrentPage = (path: string) => {
  return currentPath.value === path ||
         (path === '/dashboard' && currentPath.value === '/dashboard/');
};
</script>
<template>
  <div v-if="isClient">
    <header
      class="sticky top-0 z-50 w-full bg-white/80 backdrop-blur-lg border-b border-gray-200/50"
    >
      <div class="flex items-center justify-between px-6 py-3">
        <div class="flex items-center">
          <a href="/dashboard" class="flex items-center group">
            <img src="/logo.svg" alt="ConvertIQ Logo" class="h-7 w-auto mr-3" />
            <span class="text-xl font-semibold text-gray-900">ConvertIQ</span>
          </a>
          <!-- Navigation Links -->
          <nav class="hidden md:flex items-center space-x-1 ml-6">
            <a
              href="/dashboard"
              class="nav-link-item"
              :class="{ 'nav-link-active': isCurrentPage('/dashboard') }"
            >
            <LayoutDashboard class="w-4 h-4 mr-2"/>
              Dashboard
            </a>
            <a
              href="/pricing"
              class="nav-link-item"
              :class="{ 'nav-link-active': isCurrentPage('/pricing') }"
            >
              <CreditCard class="w-4 h-4 mr-2" />
              Pricing
            </a>
            <a
              href="/dashboard/billing"
              class="nav-link-item"
              :class="{ 'nav-link-active': isCurrentPage('/dashboard/billing') }"
            >
              <CreditCard class="w-4 h-4 mr-2" />
              Billing
            </a>
            <a
              href="/dashboard/history"
              class="nav-link-item"
              :class="{ 'nav-link-active': isCurrentPage('/dashboard/history') }"
            >
              <History class="w-4 h-4 mr-2" />
              History
            </a>
          </nav>
        </div>

        <div class="flex items-center space-x-3">
          <a
            href="/dashboard/analysis/new"
            class="nav-link-item"
            :class="{ 'nav-link-active': isCurrentPage('/dashboard/analysis/new') }"
          >
            <Plus class="w-4 h-4 mr-2"/>
            <span>New Analysis</span>
          </a>

          <div class="relative profile-menu-container">
            <button
              @click="toggleProfileMenu"
              class="flex items-center text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 hover:bg-gray-50 p-1.5 transition-all duration-200"
            >
              <div v-if="loading" class="h-8 w-8 rounded-lg bg-gray-200 animate-pulse"></div>
              <div v-else-if="user?.avatar_url" class="h-8 w-8 rounded-lg overflow-hidden">
                <img :src="user.avatar_url" alt="User avatar" class="h-full w-full object-cover">
              </div>
              <div v-else class="h-8 w-8 rounded-lg bg-primary-500 flex items-center justify-center text-white font-medium text-sm">
                {{ user?.email?.charAt(0).toUpperCase() || 'U' }}
              </div>
            </button>

            <transition
              enter-active-class="transition duration-100 ease-out"
              enter-from-class="transform scale-95 opacity-0"
              enter-to-class="transform scale-100 opacity-100"
              leave-active-class="transition duration-75 ease-in"
              leave-from-class="transform scale-100 opacity-100"
              leave-to-class="transform scale-95 opacity-0"
            >
              <div v-if="profileMenuOpen" class="origin-top-right absolute right-0 mt-2 w-56 rounded-xl shadow-lg bg-white ring-1 ring-gray-200 focus:outline-none z-10">
                <div class="py-1">
                  <div class="px-4 py-3 border-b border-gray-100">
                    <p class="text-sm font-medium text-gray-900 truncate">{{ user?.email }}</p>
                  </div>
                  <a href="/dashboard/profile" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <CircleUser class="mr-3 h-4 w-4"/>
                    <X class="w-4 h-4"/>
                    Your Profile
                  </a>
                  <button @click="signOut" class="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors text-left">
                    <LogOut class="mr-3 h-4 w-4"/>
                    Sign out
                  </button>
                </div>
              </div>
            </transition>
          </div>
        </div>
      </div>
    </header>
  </div>
  <div v-else>
    <!-- SSR Placeholder -->
    <header class="sticky top-0 z-50 w-full bg-white/80 backdrop-blur-lg border-b border-gray-200/50">
      <div class="flex items-center justify-between px-6 py-3">
        <div class="flex items-center">
          <a href="/dashboard" class="flex items-center group">
            <img src="/logo.svg" alt="ConvertIQ Logo" class="h-10 w-auto mr-3" />
            <span class="text-xl font-semibold text-gray-900">ConvertIQ</span>
          </a>
        </div>
        <div class="flex items-center space-x-3">
           <div class="h-9 w-24 rounded-lg bg-gray-200/80 animate-pulse"></div>
           <div class="h-9 w-9 rounded-lg bg-gray-200/80 animate-pulse"></div>
        </div>
      </div>
    </header>
  </div>
</template>