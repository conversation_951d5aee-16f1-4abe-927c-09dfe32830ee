export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  type: 'package' | 'addon';
  features: ProductFeature[];
  stripe_price_id: string;
  is_popular?: boolean;
}

export interface ProductFeature {
  name: string;
  description: string;
  included: boolean;
  limit?: number;
}

export interface UserPurchase {
  id: string;
  user_id: string;
  product_id: string;
  package_type: string;
  category: string;
  analysis_credits: number;
  chat_messages: number;
  reruns: number;
  amount_paid: number;
  currency: string;
  purchase_date: string;
  is_active: boolean;
  expires_at?: string;
}

export interface UserBalance {
  analysis_credits: number;
  chat_messages: number;
  reruns: number;
  purchased_packages: UserPurchase[];
}

export interface AnalysisPackage {
  id: string;
  name: string;
  description: string;
  price: number;
  features: {
    has_summary: boolean;
    has_screenshot: boolean;
    has_score: boolean;
    has_performance: boolean;
    has_suggestions: boolean;
    has_proscons: boolean;
    has_leadinsights: boolean;
    has_seo: boolean;
    chat_messages: number;
    reruns: number;
  };
  stripe_price_id: string;
}