<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMotion } from '@vueuse/motion';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/supabase';
import { useAnalysisRetry } from '../../composables/useAnalysisRetry';
import PerformanceTab from './PerformanceTab.vue';
import SEOAnalysisTab from './SEOAnalysisTab.vue';
import ProsConsTab from './ProsConsTab.vue';
import LeadInsightsTab from './LeadInsightsTab.vue';
import ChatTab from './ChatTab.vue';
import PageSummaryTab from './PageSummaryTab.vue';
import SuggestionsTab from './SuggestionsTab.vue';
import AnalysisProgressTracker from './AnalysisProgressTracker.vue';
import PaywallOverlay from './PaywallOverlay.vue';
import RerunAnalysisButton from './RerunAnalysisButton.vue';
import ScrollableTabs from '../ui/ScrollableTabs.vue';
import { BillingService } from '../../lib/billing-service';
import {
  ArrowLeft,
  MessageCircle,
  Eye,
  Lightbulb,
  Zap,
  Search,
  BarChart3,
  Users,
  Camera,
  TrendingUp,
  AlertTriangle,
  FileText,
  Fullscreen,
  RefreshCw
} from 'lucide-vue-next';

const props = defineProps<{
  analysisId: string;
}>();

type Analysis = Database['public']['Tables']['analyses']['Row'];
type Suggestion = Database['public']['Tables']['suggestions']['Row'];

const analysis = ref<Analysis | null>(null);
const analysisPermissions = ref<any>(null);
const suggestions = ref<Suggestion[]>([]);
const loading = ref(true);
const error = ref('');
const activeTab = ref('summary');
const isScreenshotModalOpen = ref(false);

// Initialize retry functionality
const {
  analysisSteps,
  getFailedSteps,
  retryAnalysisStep,
  canRetryStep,
  getStepDisplayName,
  getStepTabMapping,
  loadAnalysisProgress,
  subscribeToProgressUpdates,
  getRetryState
} = useAnalysisRetry();

// Handle retry step
const handleRetryStep = async (stepNumber: number) => {
  const success = await retryAnalysisStep(props.analysisId, stepNumber);
  if (success) {
    // Reload analysis data to reflect changes
    await loadAnalysisData();
  }
};

const openScreenshotModal = () => {
  if (analysis.value?.screenshot_url) {
    isScreenshotModalOpen.value = true;
  }
};

const closeScreenshotModal = () => {
  isScreenshotModalOpen.value = false;
};

onMounted(async () => {
  await loadAnalysisData();
  await loadAnalysisPermissions();

  // Subscribe to real-time progress updates
  subscribeToProgressUpdates(props.analysisId, (step) => {
    console.log('Progress update received:', step);
    // Optionally reload data when steps complete
    if (step.status === 'completed') {
      loadAnalysisData();
    }
  });

  // Listen for analysis completion events from the full-screen progress tracker
  document.addEventListener('analysis-complete', (event: Event) => {
    const customEvent = event as CustomEvent;
    if (customEvent.detail?.analysisId === props.analysisId) {
      console.log('Analysis completed, refreshing all data...');
      refreshAllData();
    }
  });
});

// Function to refresh all data after analysis completion
const refreshAllData = async () => {
  try {
    // Reload main analysis data
    await loadAnalysisData();

    // Force refresh all tab components by triggering a re-render
    // This ensures all tabs get fresh data from the database
    const tabComponents = document.querySelectorAll('[data-tab-component]');
    tabComponents.forEach(component => {
      // Trigger a custom refresh event that tab components can listen to
      const refreshEvent = new CustomEvent('refresh-tab-data', {
        detail: { analysisId: props.analysisId }
      });
      component.dispatchEvent(refreshEvent);
    });

    console.log('All data refreshed successfully');
  } catch (error) {
    console.error('Error refreshing data:', error);
  }
};

const loadAnalysisData = async () => {
  try {
    loading.value = true;
    error.value = '';

    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      throw new Error('User not authenticated');
    }

    // Fetch analysis data
    const { data: analysisData, error: analysisError } = await supabase
      .from('analyses')
      .select('*')
      .eq('id', props.analysisId)
      .eq('user_id', userData.user.id)
      .single();

    if (analysisError) {
      throw new Error('Analysis not found or access denied');
    }

    analysis.value = analysisData;

    // Fetch suggestions
    const { data: suggestionsData, error: suggestionsError } = await supabase
      .from('suggestions')
      .select('*')
      .eq('analysis_id', props.analysisId)
      .order('priority', { ascending: true });

    if (suggestionsError) {
      console.error('Error loading suggestions:', suggestionsError);
    } else {
      suggestions.value = suggestionsData || [];
    }

    // Load analysis progress for retry functionality
    await loadAnalysisProgress(props.analysisId);

  } catch (e) {
    console.error('Error loading analysis:', e);
    error.value = e instanceof Error ? e.message : 'Failed to load analysis';
  } finally {
    loading.value = false;
  }
};

const loadAnalysisPermissions = async () => {
  try {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) return;

    analysisPermissions.value = await BillingService.getAnalysisPermissions(
      userData.user.id, 
      props.analysisId
    );
  } catch (error) {
    console.error('Error loading analysis permissions:', error);
  }
};

const isTabLocked = (tabId: string) => {
  if (!analysisPermissions.value) return false;
  
  switch (tabId) {
    case 'performance': return !analysisPermissions.value.has_performance;
    case 'suggestions': return !analysisPermissions.value.has_suggestions;
    case 'proscons': return !analysisPermissions.value.has_proscons;
    case 'leads': return !analysisPermissions.value.has_leadinsights;
    case 'seo': return !analysisPermissions.value.has_seo;
    default: return false;
  }
};

const getTabOverlayInfo = (tabId: string) => {
  const overlayInfo = {
    performance: {
      featureName: 'Performance Analysis',
      packageRequired: 'Professional',
      description: 'Get detailed Core Web Vitals analysis, Lighthouse metrics, and performance optimization recommendations.',
      benefits: [
        'Core Web Vitals (LCP, FID, CLS)',
        'Lighthouse performance audit',
        'Speed optimization tips',
        'Mobile performance insights'
      ]
    },
    suggestions: {
      featureName: 'AI Suggestions',
      packageRequired: 'Professional',
      description: 'Receive AI-powered, actionable recommendations to improve your conversion rates.',
      benefits: [
        'AI-generated improvement suggestions',
        'Priority-ranked recommendations',
        'Implementation guidance',
        'Expected impact estimates'
      ]
    },
    proscons: {
      featureName: 'Pros & Cons Analysis',
      packageRequired: 'Professional',
      description: 'Comprehensive analysis of your website\'s strengths and areas for improvement.',
      benefits: [
        'Detailed strengths analysis',
        'Conversion barrier identification',
        'Evidence-based insights',
        'Business impact assessment'
      ]
    },
    leads: {
      featureName: 'Lead Insights',
      packageRequired: 'Premium',
      description: 'Advanced lead generation analysis with qualification questions and business impact forecasts.',
      benefits: [
        'Lead qualification questions',
        'Target audience analysis',
        'Conversion barrier identification',
        'Business growth roadmap'
      ]
    },
    seo: {
      featureName: 'SEO Analysis',
      packageRequired: 'Professional',
      description: 'Technical SEO audit with actionable recommendations to improve search visibility.',
      benefits: [
        'Technical SEO audit',
        'Meta tag optimization',
        'Search ranking factors',
        'Competitor analysis insights'
      ]
    }
  };
  
  return overlayInfo[tabId as keyof typeof overlayInfo];
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: 'numeric'
  }).format(date);
};

const getGradeInfo = (score: number) => {
  if (score >= 90) return { grade: 'A', color: 'text-green-600', bg: 'bg-green-50', progress: 'bg-green-500' };
  if (score >= 80) return { grade: 'B', color: 'text-blue-600', bg: 'bg-blue-50', progress: 'bg-blue-500' };
  if (score >= 70) return { grade: 'C', color: 'text-yellow-600', bg: 'bg-yellow-50', progress: 'bg-yellow-500' };
  if (score >= 60) return { grade: 'D', color: 'text-orange-600', bg: 'bg-orange-50', progress: 'bg-orange-500' };
  return { grade: 'F', color: 'text-red-600', bg: 'bg-red-50', progress: 'bg-red-500' };
};

// Removed unused suggestion-related functions as they're now in SuggestionsTab component

const overallGradeInfo = computed(() => {
  if (!analysis.value) {
    return { grade: 'N/A', color: 'text-gray-600', bg: 'bg-gray-50', score: 0, progress: 'bg-gray-200' };
  }

  const conversionScore = (analysis.value.score || 0) * 10; // Normalize to 100
  const scores = [
    conversionScore,
    analysis.value.performance_score || 0,
    analysis.value.seo_score || 0
  ].filter(score => score > 0);

  if (scores.length === 0) {
    return { grade: 'N/A', color: 'text-gray-600', bg: 'bg-gray-50', score: 0, progress: 'bg-gray-200' };
  }

  const overallScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  const gradeInfo = getGradeInfo(overallScore);

  return { ...gradeInfo, score: overallScore / 10 };
});

const tabs = [
  { id: 'summary', name: 'Page Summary', icon: FileText },
  { id: 'suggestions', name: 'Suggestions', icon: Lightbulb },
  { id: 'proscons', name: 'Pros & Cons', icon: BarChart3 },
  { id: 'performance', name: 'Performance', icon: Zap },
  // { id: 'seo', name: 'SEO Insights', icon: Search }, - Needs more work
  { id: 'leads', name: 'Lead Insights', icon: Users },
  { id: 'chat', name: 'AI Chat', icon: MessageCircle }
];

// Check if AI content is still being generated
const isContentGenerating = computed(() => {
  if (!analysis.value) return false;

  // Check if generation is in progress or not all content is generated
  // Use type assertion to access new columns that may not be in the type definition yet
  const analysisWithNewFields = analysis.value as any;

  const isGenerating = analysisWithNewFields.generation_status === 'in_progress' ||
                      analysisWithNewFields.generation_status === 'pending';

  console.log('isContentGenerating check:', {
    generation_status: analysisWithNewFields.generation_status,
    all_content_generated: analysisWithNewFields.all_content_generated,
    isGenerating
  });

  return isGenerating;
});

// Handle progress completion
const onProgressComplete = async () => {
  console.log('Progress completed, reloading analysis data...');
  // Reload analysis data when generation is complete
  await loadAnalysisData();
  console.log('Analysis data reloaded, generation status:', (analysis.value as any)?.generation_status);
};


</script>

<template>
  <div class="min-h-screen bg-gray-50 pt-16">

    <!-- Loading State -->
    <div v-if="loading" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="animate-pulse">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div class="h-32 bg-gray-200 rounded mb-6"></div>
              <div class="space-y-3">
                <div class="h-4 bg-gray-200 rounded"></div>
                <div class="h-4 bg-gray-200 rounded w-5/6"></div>
              </div>
            </div>
          </div>
          <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
              <div class="space-y-4">
                <div class="h-24 bg-gray-200 rounded"></div>
                <div class="h-24 bg-gray-200 rounded"></div>
                <div class="h-24 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-red-50 border border-red-200 rounded-lg p-6">
        <div class="flex">
          <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h3 class="text-red-800 font-medium">Error Loading Analysis</h3>
            <p class="text-red-700 mt-1">{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Screenshot Modal -->
    <Transition name="screenshot-modal">
      <div v-if="isScreenshotModalOpen" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75" @click="closeScreenshotModal">
        <div class="relative max-w-4xl max-h-full modal-content" @click.stop>
          <img :src="analysis?.screenshot_url || ''" alt="Expanded Screenshot" class="rounded-lg shadow-xl">
          <button @click="closeScreenshotModal" class="absolute -top-4 -right-4 bg-white rounded-full p-2 text-gray-700 hover:bg-gray-200 transition">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
          </button>
        </div>
      </div>
    </Transition>

    <!-- Main Content -->
    <div
      v-if="analysis"
      class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
    >
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Sidebar - Overview -->
        <div
          class="lg:col-span-1"
        >
          <div class="bg-white rounded-lg shadow overflow-hidden">
            <!-- Screenshot at the very top - no padding/margin -->
            <div class="w-full cursor-pointer relative group" @click="openScreenshotModal">
              <div v-if="analysis.screenshot_url" class="w-full aspect-video">
                <img
                  :src="analysis.screenshot_url"
                  :alt="`Screenshot of ${analysis.url}`"
                  class="w-full h-full object-cover transition-all duration-300 group-hover:blur-sm group-hover:brightness-75"
                />
                <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300">
                  <Fullscreen class="w-12 h-12 text-gray-200 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </div>
              <div v-else class="w-full aspect-video bg-gray-100 flex items-center justify-center">
                <div class="text-center text-gray-400">
                  <Camera class="w-12 h-12 mx-auto mb-2" />
                  <p class="text-sm">Screenshot not available</p>
                </div>
              </div>
            </div>

            <!-- Overview Header -->
            <div class="p-6 border-b border-gray-200">
              <div class="flex items-center mb-2">
                <Eye class="w-5 h-5 text-gray-400 mr-2" />
                <h2 class="text-lg font-semibold text-gray-900">Overview</h2>
              </div>
            </div>

            <!-- Page Info -->
            <div class="p-6 border-b border-gray-200">
              <h3 class="font-medium text-gray-900 mb-1">{{ analysis.title }}</h3>
              <a :href="analysis.url" target="_blank" class="text-sm text-blue-600 hover:text-blue-800 break-all">
                {{ analysis.url }}
              </a>
            </div>

            <!-- Failed Steps Retry Section -->
            <div v-if="getFailedSteps().length > 0" class="p-6 border-b border-gray-200">
              <div class="flex items-center mb-4">
                <AlertTriangle class="w-5 h-5 text-yellow-500 mr-2" />
                <h4 class="font-medium text-gray-900">Analysis Issues</h4>
              </div>
              <p class="text-sm text-gray-600 mb-4">
                Some analysis steps failed. You can retry them individually:
              </p>
              <div class="space-y-2">
                <div
                  v-for="step in getFailedSteps()"
                  :key="step.stepNumber"
                  class="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                >
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">{{ getStepDisplayName(step.stepNumber) }}</p>
                    <p class="text-xs text-gray-600 mt-1">{{ step.errorMessage }}</p>
                  </div>
                  <button
                    @click="handleRetryStep(step.stepNumber)"
                    :disabled="getRetryState(step.stepNumber).isRetrying"
                    class="ml-3 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <RefreshCw
                      :class="['w-3 h-3 mr-1', getRetryState(step.stepNumber).isRetrying ? 'animate-spin' : '']"
                    />
                    {{ getRetryState(step.stepNumber).isRetrying ? 'Retrying...' : 'Retry' }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Overall Grade -->
            <div class="p-6 border-b border-gray-200">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <h4 class="font-medium text-gray-900 mr-2">Overall Grade</h4>
                  <div class="relative group">
                    <Info class="w-4 h-4 text-gray-400 cursor-pointer" />
                    <div class="absolute bottom-full mb-2 w-64 bg-gray-800 text-white text-xs rounded py-2 px-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                      This grade is a weighted average of the SEO, Performance, and Conversion scores, providing a holistic view of your website's health.
                      <div class="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-x-4 border-x-transparent border-t-4 border-t-gray-800"></div>
                    </div>
                  </div>
                </div>
                <div
                  class="w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold"
                  :class="overallGradeInfo.bg + ' ' + overallGradeInfo.color"
                >
                  {{ overallGradeInfo.grade }}
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="text-3xl font-bold" :class="overallGradeInfo.color">
                  {{ overallGradeInfo.score.toFixed(1) }}
                </span>
                <span class="text-gray-500 ml-1">/10</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div
                  class="h-2 rounded-full transition-all duration-500"
                  :class="overallGradeInfo.progress"
                  :style="{ width: `${overallGradeInfo.score * 10}%` }"
                ></div>
              </div>
            </div>

            <!-- Conversion Rate -->
            <div class="p-6 border-b border-gray-200">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <h4 class="font-medium text-gray-900 mr-2">Estimated Conversion Rate</h4>
                  <div class="relative group">
                    <Info class="w-4 h-4 text-gray-400 cursor-pointer" />
                    <div class="absolute bottom-full mb-2 w-64 bg-gray-800 text-white text-xs rounded py-2 px-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                      This is an AI-powered estimate of your website's conversion rate based on its design, content, and user experience.
                      <div class="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-x-4 border-x-transparent border-t-4 border-t-gray-800"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="text-3xl font-bold" :class="getGradeInfo((analysis.score || 0) * 10).color">
                  {{ analysis.score || 0 }}%
                </span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div
                  class="h-2 rounded-full transition-all duration-500"
                  :class="getGradeInfo((analysis.score || 0) * 10).progress"
                  :style="{ width: `${(analysis.score || 0)}%` }"
                ></div>
              </div>
            </div>

            <!-- Stats -->
            <div class="p-6">
              <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-3 bg-blue-50 rounded-lg transition-all duration-300 hover:bg-blue-100">
                  <TrendingUp class="w-6 h-6 text-blue-600 mx-auto mb-2" />
                  <div class="text-2xl font-bold text-gray-900">{{ analysis.suggestions_count || 0 }}</div>
                  <div class="text-sm text-gray-500">Suggestions</div>
                </div>
                <div class="text-center p-3 bg-red-50 rounded-lg transition-all duration-300 hover:bg-red-100">
                  <AlertTriangle class="w-6 h-6 text-red-600 mx-auto mb-2" />
                  <div class="text-2xl font-bold text-red-600">{{ analysis.priority_issues_count || 0 }}</div>
                  <div class="text-sm text-gray-500">Priority Issues</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Content - Tabbed Interface -->
        <div
          class="lg:col-span-2"
        >
          <div class="bg-white rounded-lg shadow">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200">
              <ScrollableTabs :tabs="tabs" v-model:activeTab="activeTab" />
            </div>

            <!-- Tab Content with smooth transitions -->
            <div class="p-6">
              <!-- Rerun Button -->
              <div v-if="analysis" class="mb-6 flex justify-end">
                <RerunAnalysisButton
                  :analysis-id="analysis.id"
                  :website-url="analysis.url"
                  :current-rerun-number="analysis.title?.match(/rerun (\d+)/)?.[1] ? parseInt(analysis.title.match(/rerun (\d+)/)![1]) : 0"
                />
              </div>

              <!-- Show progress tracker if content is still being generated -->
              <AnalysisProgressTracker
                v-if="isContentGenerating"
                :analysisId="props.analysisId"
                :onComplete="onProgressComplete"
                class="mb-6"
              />

              <Transition
                name="tab-fade"
                mode="out-in"
                appear
              >
                <div class="relative">
                  <!-- Page Summary Tab -->
                  <PageSummaryTab v-if="activeTab === 'summary' && analysis" key="summary" :analysis="analysis" />

                  <!-- Suggestions Tab -->
                  <div v-else-if="activeTab === 'suggestions' && analysis" key="suggestions" class="relative">
                    <SuggestionsTab :analysis="analysis" />
                    <PaywallOverlay
                      v-if="isTabLocked('suggestions')"
                      v-bind="getTabOverlayInfo('suggestions')"
                    />
                  </div>

                  <!-- Performance Tab -->
                  <div v-else-if="activeTab === 'performance' && analysis" key="performance" class="relative">
                    <PerformanceTab :analysis="analysis" />
                    <PaywallOverlay
                      v-if="isTabLocked('performance')"
                      v-bind="getTabOverlayInfo('performance')"
                    />
                  </div>

                  <!-- SEO Analysis Tab -->
                  <div v-else-if="activeTab === 'seo' && analysis" key="seo" class="relative">
                    <SEOAnalysisTab :analysis="analysis" />
                    <PaywallOverlay
                      v-if="isTabLocked('seo')"
                      v-bind="getTabOverlayInfo('seo')"
                    />
                  </div>

                  <!-- Pros & Cons Tab -->
                  <div v-else-if="activeTab === 'proscons' && analysis" key="proscons" class="relative">
                    <ProsConsTab :analysis="analysis" />
                    <PaywallOverlay
                      v-if="isTabLocked('proscons')"
                      v-bind="getTabOverlayInfo('proscons')"
                    />
                  </div>

                  <!-- Lead Insights Tab -->
                  <div v-else-if="activeTab === 'leads' && analysis" key="leads" class="relative">
                    <LeadInsightsTab :analysis="analysis" />
                    <PaywallOverlay
                      v-if="isTabLocked('leads')"
                      v-bind="getTabOverlayInfo('leads')"
                    />
                  </div>

                  <!-- AI Chat Tab -->
                  <ChatTab 
                    v-else-if="activeTab === 'chat' && analysis" 
                    key="chat" 
                    :analysis="analysis"
                    :chat-limit="analysisPermissions?.chat_messages_remaining || 0"
                  />
                </div>
              </Transition>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Screenshot Modal -->
    <Transition name="screenshot-modal">
      <div v-if="isScreenshotModalOpen" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75" @click="closeScreenshotModal">
        <div class="relative max-w-4xl max-h-full modal-content" @click.stop>
          <img :src="analysis?.screenshot_url || ''" alt="Expanded Screenshot" class="rounded-lg shadow-xl">
          <button @click="closeScreenshotModal" class="absolute -top-4 -right-4 bg-white rounded-full p-2 text-gray-700 hover:bg-gray-200 transition">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
/* Tab transition animations */
.tab-fade-enter-active,
.tab-fade-leave-active {
  transition: all 0.4s ease-in-out;
}

.tab-fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.tab-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.tab-fade-enter-to,
.tab-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.screenshot-modal-enter-active,
.screenshot-modal-leave-active {
  transition: opacity 0.3s ease;
}

.screenshot-modal-enter-from,
.screenshot-modal-leave-to {
  opacity: 0;
}

.screenshot-modal-enter-active .modal-content,
.screenshot-modal-leave-active .modal-content {
  transition: transform 0.3s ease;
}

.screenshot-modal-enter-from .modal-content {
  transform: scale(0.95);
}

.screenshot-modal-leave-to .modal-content {
  transform: scale(0.95);
}
</style>
