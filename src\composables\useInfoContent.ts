import { ref, computed } from 'vue';
import { supabase } from '../lib/supabase';

interface InfoContent {
  id: string;
  content_key: string;
  content_type: 'tooltip' | 'help_text' | 'modal_content' | 'guide_step';
  title?: string;
  content_text: string;
  content_html?: string;
  icon_name?: string;
  position_hint?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  trigger_event?: 'hover' | 'click' | 'focus' | 'auto';
  display_duration_ms?: number;
  target_audience?: 'beginner' | 'intermediate' | 'advanced' | 'all';
  page_context?: string;
}

const infoContentCache = ref<Record<string, InfoContent>>({});
const isLoading = ref(false);
const error = ref<string | null>(null);

// Default content for common metrics and features
const defaultContent: Record<string, Partial<InfoContent>> = {
  'performance-score': {
    title: 'Performance Score',
    content_text: 'Measures loading performance including First Contentful Paint, Largest Contentful Paint, and other speed metrics. Higher scores indicate faster loading times. Scored 0-100.',
    position_hint: 'top'
  },
  'accessibility-score': {
    title: 'Accessibility Score',
    content_text: 'Evaluates how accessible your website is to users with disabilities. Includes checks for alt text, color contrast, keyboard navigation, and screen reader compatibility. Scored 0-100.',
    position_hint: 'top'
  },
  'seo-score': {
    title: 'SEO Score',
    content_text: 'Evaluates basic SEO factors including meta descriptions, title tags, crawlability, and mobile-friendliness that affect search engine rankings. Scored 0-100.',
    position_hint: 'top'
  },
  'best-practices-score': {
    title: 'Best Practices Score',
    content_text: 'Checks for modern web development best practices including HTTPS usage, console errors, deprecated APIs, and security vulnerabilities. Scored 0-100.',
    position_hint: 'top'
  },
  'lcp': {
    title: 'Largest Contentful Paint (LCP)',
    content_text: 'Measures how long it takes for the main content to load. Good: ≤2.5s, Needs Improvement: ≤4s, Poor: >4s. This metric is crucial for user experience.',
    position_hint: 'top'
  },
  'fid': {
    title: 'First Input Delay (FID)',
    content_text: 'Measures how long it takes for the page to respond to user interactions. Good: ≤100ms, Needs Improvement: ≤300ms, Poor: >300ms. Lower is better.',
    position_hint: 'top'
  },
  'cls': {
    title: 'Cumulative Layout Shift (CLS)',
    content_text: 'Measures visual stability - how much content moves around during loading. Good: ≤0.1, Needs Improvement: ≤0.25, Poor: >0.25. Lower is better.',
    position_hint: 'top'
  },
  'conversion-score': {
    title: 'Conversion Score',
    content_text: 'Predicts how well your page converts visitors into leads or customers. Based on design elements, trust signals, call-to-actions, and user experience factors. Scored 0-10.',
    position_hint: 'top'
  },
  'lead-score': {
    title: 'Lead Generation Score',
    content_text: 'Combines conversion optimization (50%), performance (30%), and SEO (20%) to predict lead capture potential. Higher scores indicate better lead generation capability. Scored 0-10.',
    position_hint: 'top'
  },
  'target-audience': {
    title: 'Target Audience Analysis',
    content_text: 'AI analysis of page content to identify the most likely prospects and their characteristics based on messaging, positioning, and content strategy.',
    position_hint: 'top'
  },
  'conversion-priority': {
    title: 'Conversion Priority',
    content_text: 'Shows which areas to focus on first for maximum conversion impact. Based on current scores, improvement potential, and business impact analysis.',
    position_hint: 'top'
  },
  'core-web-vitals': {
    title: 'Core Web Vitals',
    content_text: 'Google\'s key metrics for measuring user experience: LCP (loading), FID (interactivity), and CLS (visual stability). These directly impact search rankings.',
    position_hint: 'top'
  },
  'lighthouse-report': {
    title: 'Lighthouse Analysis',
    content_text: 'Lighthouse analyzes your website across four key areas: Performance (loading speed), Accessibility (usability for all users), Best Practices (modern web standards), and SEO (search engine optimization).',
    position_hint: 'top'
  },
  'ai-insights': {
    title: 'AI-Powered Insights',
    content_text: 'Advanced AI analysis that provides contextual recommendations based on your specific business type, target audience, and conversion goals.',
    position_hint: 'top'
  },
  'business-impact': {
    title: 'Business Impact Analysis',
    content_text: 'Estimates the potential revenue and conversion improvements from implementing the recommended optimizations, based on industry benchmarks and performance data.',
    position_hint: 'top'
  }
};

export function useInfoContent() {
  /**
   * Load info content from database or use defaults
   */
  const loadInfoContent = async (contentKeys: string[]): Promise<void> => {
    if (isLoading.value) return;

    isLoading.value = true;
    error.value = null;

    try {
      // Check which keys we don't have cached
      const uncachedKeys = contentKeys.filter(key => !infoContentCache.value[key]);
      
      if (uncachedKeys.length > 0) {
        const { data, error: fetchError } = await supabase
          .from('ui_info_content')
          .select('*')
          .in('content_key', uncachedKeys);

        if (fetchError) {
          console.warn('Failed to load info content from database:', fetchError);
          // Fall back to defaults
        } else if (data) {
          // Cache the loaded content
          data.forEach(item => {
            infoContentCache.value[item.content_key] = item;
          });
        }
      }

      // Fill in defaults for any missing content
      contentKeys.forEach(key => {
        if (!infoContentCache.value[key] && defaultContent[key]) {
          infoContentCache.value[key] = {
            id: `default-${key}`,
            content_key: key,
            content_type: 'tooltip',
            ...defaultContent[key]
          } as InfoContent;
        }
      });

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load info content';
      console.error('Error loading info content:', err);
      
      // Fall back to defaults
      contentKeys.forEach(key => {
        if (defaultContent[key]) {
          infoContentCache.value[key] = {
            id: `default-${key}`,
            content_key: key,
            content_type: 'tooltip',
            ...defaultContent[key]
          } as InfoContent;
        }
      });
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Get info content by key
   */
  const getInfoContent = (contentKey: string): InfoContent | null => {
    return infoContentCache.value[contentKey] || null;
  };

  /**
   * Get content text with fallback
   */
  const getContentText = (contentKey: string): string => {
    const content = getInfoContent(contentKey);
    return content?.content_text || `Information about ${contentKey}`;
  };

  /**
   * Get content title with fallback
   */
  const getContentTitle = (contentKey: string): string | undefined => {
    const content = getInfoContent(contentKey);
    return content?.title;
  };

  /**
   * Get content HTML if available
   */
  const getContentHtml = (contentKey: string): string | undefined => {
    const content = getInfoContent(contentKey);
    return content?.content_html;
  };

  /**
   * Check if content should be displayed as HTML
   */
  const isHtmlContent = (contentKey: string): boolean => {
    const content = getInfoContent(contentKey);
    return !!(content?.content_html);
  };

  /**
   * Get position hint for tooltip
   */
  const getPositionHint = (contentKey: string): 'top' | 'bottom' | 'left' | 'right' | 'auto' => {
    const content = getInfoContent(contentKey);
    return content?.position_hint || 'auto';
  };

  /**
   * Get trigger event for tooltip
   */
  const getTriggerEvent = (contentKey: string): 'hover' | 'click' | 'both' => {
    const content = getInfoContent(contentKey);
    const trigger = content?.trigger_event;
    if (trigger === 'click') return 'click';
    if (trigger === 'hover') return 'hover';
    return 'hover'; // default
  };

  /**
   * Track interaction with info content
   */
  const trackInteraction = async (
    contentKey: string, 
    interactionType: 'tooltip_view' | 'help_click',
    duration?: number
  ): Promise<void> => {
    try {
      await supabase
        .from('ui_interaction_tracking')
        .insert({
          session_id: `session-${Date.now()}`,
          interaction_type: interactionType,
          content_key: contentKey,
          page_path: window.location.pathname,
          component_name: 'InfoBox',
          interaction_duration_ms: duration,
          user_agent: navigator.userAgent,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`
        });
    } catch (err) {
      console.warn('Failed to track interaction:', err);
    }
  };

  /**
   * Preload common info content
   */
  const preloadCommonContent = async (): Promise<void> => {
    const commonKeys = [
      'performance-score',
      'accessibility-score', 
      'seo-score',
      'best-practices-score',
      'lcp',
      'fid', 
      'cls',
      'conversion-score',
      'lead-score',
      'core-web-vitals'
    ];
    
    await loadInfoContent(commonKeys);
  };

  return {
    infoContentCache: computed(() => infoContentCache.value),
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),
    loadInfoContent,
    getInfoContent,
    getContentText,
    getContentTitle,
    getContentHtml,
    isHtmlContent,
    getPositionHint,
    getTriggerEvent,
    trackInteraction,
    preloadCommonContent
  };
}
