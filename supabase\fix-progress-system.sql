-- ConvertIQ Progress System Rework: current_process field implementation
-- This migration adds the new current_process field and ensures proper database structure

-- Add current_process field to analyses table if it doesn't exist
ALTER TABLE public.analyses
ADD COLUMN IF NOT EXISTS current_process integer DEFAULT 0;

-- Add is_completed field if it doesn't exist
ALTER TABLE public.analyses
ADD COLUMN IF NOT EXISTS is_completed boolean DEFAULT false;

-- Update any existing analyses to have proper default values
UPDATE public.analyses
SET current_process = 0
WHERE current_process IS NULL;

UPDATE public.analyses
SET is_completed = false
WHERE is_completed IS NULL;

-- Drop unused progress tables (if they exist)

DROP TABLE IF EXISTS public.progress_status_updates CASCADE;
DROP TABLE IF EXISTS public.live_progress_feed CASCADE;
DROP TABLE IF EXISTS public.analysis_generation_progress CASCADE;

-- Create index for faster progress queries
CREATE INDEX IF NOT EXISTS idx_analyses_progress ON public.analyses(current_process, is_completed);

-- Verify ai_insights table exists for pros/cons data (should already exist)
-- This table stores strengths and weaknesses for the Pros & Cons tab
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ai_insights') THEN
        RAISE NOTICE 'ai_insights table missing - this should not happen with current schema';
    END IF;
END $$;

-- Ensure suggestion_categories table has default categories for ai_insights
INSERT INTO public.suggestion_categories (name, icon, color, description)
VALUES
    ('conversion', 'Target', '#3B82F6', 'Conversion optimization insights'),
    ('performance', 'Zap', '#10B981', 'Performance-related insights'),
    ('usability', 'Users', '#8B5CF6', 'User experience insights'),
    ('content', 'FileText', '#F59E0B', 'Content and messaging insights')
ON CONFLICT (name) DO NOTHING;

-- Add performance metrics enhancements for comprehensive analysis
-- Ensure TTFB and other advanced metrics are captured
ALTER TABLE public.lighthouse_performance_metrics
ADD COLUMN IF NOT EXISTS time_to_first_byte_ms numeric,
ADD COLUMN IF NOT EXISTS server_response_time_ms numeric,
ADD COLUMN IF NOT EXISTS render_blocking_resources jsonb DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS unused_css_rules jsonb DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS image_optimization_opportunities jsonb DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS network_requests_count integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_transfer_size_bytes bigint DEFAULT 0,
ADD COLUMN IF NOT EXISTS main_thread_work_breakdown jsonb DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS third_party_summary jsonb DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS resource_summary jsonb DEFAULT '{}'::jsonb;

-- Create advanced performance metrics table for WebPageTest.org-style metrics
CREATE TABLE IF NOT EXISTS public.advanced_performance_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,

  -- Network timing metrics
  dns_lookup_time_ms numeric,
  tcp_connect_time_ms numeric,
  ssl_handshake_time_ms numeric,
  time_to_first_byte_ms numeric,
  download_time_ms numeric,

  -- Resource loading metrics
  total_requests integer DEFAULT 0,
  total_bytes bigint DEFAULT 0,
  html_requests integer DEFAULT 0,
  html_bytes bigint DEFAULT 0,
  css_requests integer DEFAULT 0,
  css_bytes bigint DEFAULT 0,
  js_requests integer DEFAULT 0,
  js_bytes bigint DEFAULT 0,
  image_requests integer DEFAULT 0,
  image_bytes bigint DEFAULT 0,
  font_requests integer DEFAULT 0,
  font_bytes bigint DEFAULT 0,

  -- Performance budget analysis
  performance_budget_status jsonb DEFAULT '{}'::jsonb,
  resource_hints_analysis jsonb DEFAULT '{}'::jsonb,
  critical_resource_chains jsonb DEFAULT '[]'::jsonb,

  -- Advanced timing metrics
  dom_content_loaded_ms numeric,
  load_event_ms numeric,
  fully_loaded_ms numeric,

  -- User experience metrics
  visual_complete_ms numeric,
  speed_index_ms numeric,
  perceptual_speed_index_ms numeric,

  -- AI-powered insights
  ai_performance_score numeric CHECK (ai_performance_score >= 0 AND ai_performance_score <= 10),
  ai_optimization_priority text,
  ai_performance_summary text,

  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),

  CONSTRAINT advanced_performance_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT advanced_performance_metrics_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);

-- Update existing analyses to have proper default values
UPDATE public.analyses
SET current_step = 0, is_completed = true
WHERE current_step IS NULL OR is_completed IS NULL;

-- Update RLS policies for new fields
ALTER POLICY "Users can view their own analyses" ON public.analyses USING (auth.uid() = user_id);
ALTER POLICY "Users can update their own analyses" ON public.analyses USING (auth.uid() = user_id);