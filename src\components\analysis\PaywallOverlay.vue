<script setup lang="ts">
import { computed } from 'vue';
import { Lock, Crown, Zap, TrendingUp, ArrowRight } from 'lucide-vue-next';

const props = defineProps<{
  featureName: string;
  packageRequired: string;
  description: string;
  benefits: string[];
  upgradeUrl?: string;
}>();

const getFeatureIcon = (featureName: string) => {
  switch (featureName.toLowerCase()) {
    case 'performance': return Zap;
    case 'lead insights': return TrendingUp;
    case 'ai suggestions': return Crown;
    default: return Lock;
  }
};

const getPackageColor = (packageName: string) => {
  switch (packageName.toLowerCase()) {
    case 'professional': return 'from-purple-500 to-blue-600';
    case 'premium': return 'from-yellow-500 to-orange-600';
    default: return 'from-blue-500 to-purple-600';
  }
};
</script>

<template>
  <div class="absolute inset-0 bg-white/95 backdrop-blur-sm rounded-lg flex items-center justify-center z-10">
    <div class="text-center max-w-sm mx-auto p-6">
      <!-- Feature Icon -->
      <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r flex items-center justify-center"
           :class="getPackageColor(packageRequired)">
        <component :is="getFeatureIcon(featureName)" class="w-8 h-8 text-white" />
      </div>

      <!-- Title -->
      <h3 class="text-xl font-bold text-gray-900 mb-2">
        {{ featureName }} Analysis
      </h3>
      
      <!-- Description -->
      <p class="text-gray-600 mb-4 text-sm leading-relaxed">
        {{ description }}
      </p>

      <!-- Benefits -->
      <div class="space-y-2 mb-6">
        <div 
          v-for="benefit in benefits" 
          :key="benefit"
          class="flex items-center text-sm text-gray-700"
        >
          <CheckCircle class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
          <span>{{ benefit }}</span>
        </div>
      </div>

      <!-- Package Required -->
      <div class="bg-gradient-to-r rounded-lg p-4 mb-6 text-white"
           :class="getPackageColor(packageRequired)">
        <div class="flex items-center justify-center">
          <Crown class="w-5 h-5 mr-2" />
          <span class="font-medium">{{ packageRequired }} Package Required</span>
        </div>
      </div>

      <!-- Upgrade Button -->
      <a 
        :href="upgradeUrl || '/dashboard/billing'"
        class="inline-flex items-center justify-center w-full py-3 px-4 bg-gradient-to-r text-white font-medium rounded-lg hover:shadow-lg transition-all duration-200 transform hover:scale-105"
        :class="getPackageColor(packageRequired)"
      >
        <span>Upgrade to {{ packageRequired }}</span>
        <ArrowRight class="w-4 h-4 ml-2" />
      </a>

      <p class="text-xs text-gray-500 mt-3">
        Unlock this feature and more with a package upgrade
      </p>
    </div>
  </div>
</template>