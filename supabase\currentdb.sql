-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.advanced_metrics_summary (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  lighthouse_summary text,
  performance_breakdown jsonb DEFAULT '{}'::jsonb,
  accessibility_insights text,
  best_practices_analysis text,
  seo_technical_summary text,
  mobile_performance_notes text,
  optimization_priorities jsonb DEFAULT '[]'::jsonb,
  business_impact_assessment text,
  technical_debt_analysis text,
  monitoring_recommendations jsonb DEFAULT '[]'::jsonb,
  generated_at timestamp with time zone DEFAULT now(),
  ai_generated boolean DEFAULT true,
  CONSTRAINT advanced_metrics_summary_pkey PRIMARY KEY (id),
  CONSTRAINT advanced_metrics_summary_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.advanced_performance_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  dns_lookup_time_ms numeric,
  tcp_connect_time_ms numeric,
  ssl_handshake_time_ms numeric,
  time_to_first_byte_ms numeric,
  download_time_ms numeric,
  total_requests integer DEFAULT 0,
  total_bytes bigint DEFAULT 0,
  html_requests integer DEFAULT 0,
  html_bytes bigint DEFAULT 0,
  css_requests integer DEFAULT 0,
  css_bytes bigint DEFAULT 0,
  js_requests integer DEFAULT 0,
  js_bytes bigint DEFAULT 0,
  image_requests integer DEFAULT 0,
  image_bytes bigint DEFAULT 0,
  font_requests integer DEFAULT 0,
  font_bytes bigint DEFAULT 0,
  performance_budget_status jsonb DEFAULT '{}'::jsonb,
  resource_hints_analysis jsonb DEFAULT '{}'::jsonb,
  critical_resource_chains jsonb DEFAULT '[]'::jsonb,
  dom_content_loaded_ms numeric,
  load_event_ms numeric,
  fully_loaded_ms numeric,
  visual_complete_ms numeric,
  speed_index_ms numeric,
  perceptual_speed_index_ms numeric,
  ai_performance_score numeric CHECK (ai_performance_score >= 0::numeric AND ai_performance_score <= 10::numeric),
  ai_optimization_priority text,
  ai_performance_summary text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT advanced_performance_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT advanced_performance_metrics_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_business_context (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  business_type text NOT NULL,
  industry_category text,
  target_audience_primary text NOT NULL,
  target_audience_secondary text,
  value_proposition_primary text NOT NULL,
  value_proposition_supporting jsonb DEFAULT '[]'::jsonb,
  competitive_positioning text,
  business_model_type text,
  revenue_model text,
  market_segment text,
  brand_personality jsonb DEFAULT '{}'::jsonb,
  messaging_tone text,
  trust_factors jsonb DEFAULT '[]'::jsonb,
  credibility_signals jsonb DEFAULT '[]'::jsonb,
  ai_confidence_score numeric CHECK (ai_confidence_score >= 0::numeric AND ai_confidence_score <= 1::numeric),
  generated_by_model text DEFAULT 'qwen-2.5-72b'::text,
  generation_timestamp timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_business_context_pkey PRIMARY KEY (id),
  CONSTRAINT ai_business_context_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_business_impact_forecast (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  current_conversion_estimate numeric,
  current_lead_quality_score numeric CHECK (current_lead_quality_score >= 0::numeric AND current_lead_quality_score <= 10::numeric),
  current_user_experience_score numeric CHECK (current_user_experience_score >= 0::numeric AND current_user_experience_score <= 10::numeric),
  optimization_potential_score numeric CHECK (optimization_potential_score >= 0::numeric AND optimization_potential_score <= 100::numeric),
  quick_wins_impact_estimate text,
  medium_term_impact_estimate text,
  long_term_impact_estimate text,
  revenue_impact_low_estimate numeric,
  revenue_impact_high_estimate numeric,
  payback_period_estimate text,
  competitive_advantage_opportunities jsonb DEFAULT '[]'::jsonb,
  market_positioning_insights text,
  implementation_risks jsonb DEFAULT '[]'::jsonb,
  opportunity_cost_analysis text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_business_impact_forecast_pkey PRIMARY KEY (id),
  CONSTRAINT ai_business_impact_forecast_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_chat_context (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  website_summary text NOT NULL,
  key_metrics jsonb NOT NULL DEFAULT '{}'::jsonb,
  business_context text NOT NULL,
  suggested_prompts jsonb NOT NULL DEFAULT '[]'::jsonb,
  conversation_starters jsonb NOT NULL DEFAULT '[]'::jsonb,
  technical_context jsonb NOT NULL DEFAULT '{}'::jsonb,
  optimization_opportunities jsonb NOT NULL DEFAULT '[]'::jsonb,
  ai_generated boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_chat_context_pkey PRIMARY KEY (id),
  CONSTRAINT ai_chat_context_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_conversion_roadmap (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  phase_number integer NOT NULL CHECK (phase_number >= 1),
  phase_name text NOT NULL,
  phase_description text NOT NULL,
  timeline_estimate text NOT NULL,
  effort_level text NOT NULL CHECK (effort_level = ANY (ARRAY['low'::text, 'medium'::text, 'high'::text])),
  expected_impact_description text NOT NULL,
  expected_conversion_lift_min numeric,
  expected_conversion_lift_max numeric,
  success_metrics jsonb DEFAULT '[]'::jsonb,
  implementation_tasks jsonb DEFAULT '[]'::jsonb,
  required_resources jsonb DEFAULT '[]'::jsonb,
  risk_factors jsonb DEFAULT '[]'::jsonb,
  dependencies text,
  validation_methods jsonb DEFAULT '[]'::jsonb,
  roi_estimate text,
  priority_score numeric CHECK (priority_score >= 0::numeric AND priority_score <= 100::numeric),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_conversion_roadmap_pkey PRIMARY KEY (id),
  CONSTRAINT ai_conversion_roadmap_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_insight_generation_log (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  insight_type text NOT NULL CHECK (insight_type = ANY (ARRAY['pros_cons'::text, 'lead_qualification'::text, 'performance_impact'::text, 'business_roadmap'::text, 'page_summary'::text])),
  generation_status text NOT NULL CHECK (generation_status = ANY (ARRAY['queued'::text, 'processing'::text, 'completed'::text, 'failed'::text])),
  ai_model_used text DEFAULT 'qwen-2.5-72b'::text,
  prompt_template_version text DEFAULT '1.0'::text,
  input_data_hash text,
  processing_time_ms integer,
  token_usage_input integer,
  token_usage_output integer,
  api_cost_estimate numeric,
  error_message text,
  retry_count integer DEFAULT 0,
  generated_at timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_insight_generation_log_pkey PRIMARY KEY (id),
  CONSTRAINT ai_insight_generation_log_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_insights (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  category_id uuid NOT NULL,
  insight_type text CHECK (insight_type = ANY (ARRAY['strength'::text, 'weakness'::text])),
  title text,
  description text,
  evidence text,
  impact_explanation text,
  implementation_steps text,
  business_value text,
  priority_score numeric,
  created_at timestamp with time zone DEFAULT now(),
  display_order integer DEFAULT 0,
  impact_score numeric CHECK (impact_score >= 0::numeric AND impact_score <= 10::numeric),
  effort_required text CHECK (effort_required = ANY (ARRAY['Low'::text, 'Medium'::text, 'High'::text])),
  timeline_estimate text,
  success_metrics text,
  related_metrics jsonb DEFAULT '[]'::jsonb,
  conversion_impact_estimate text,
  technical_requirements text,
  insight_category text DEFAULT 'general'::text,
  confidence_score numeric CHECK (confidence_score >= 0::numeric AND confidence_score <= 1::numeric),
  data_source text DEFAULT 'ai_analysis'::text,
  validation_status text DEFAULT 'pending'::text CHECK (validation_status = ANY (ARRAY['pending'::text, 'validated'::text, 'rejected'::text])),
  user_feedback jsonb DEFAULT '{}'::jsonb,
  implementation_complexity text CHECK (implementation_complexity = ANY (ARRAY['Low'::text, 'Medium'::text, 'High'::text])),
  CONSTRAINT ai_insights_pkey PRIMARY KEY (id),
  CONSTRAINT ai_insights_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT ai_insights_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.suggestion_categories(id)
);
CREATE TABLE public.ai_lead_qualification_framework (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  qualification_category text NOT NULL CHECK (qualification_category = ANY (ARRAY['discovery'::text, 'pain_points'::text, 'budget'::text, 'authority'::text, 'timeline'::text, 'fit_assessment'::text])),
  question_text text NOT NULL,
  question_context text NOT NULL,
  expected_response_type text CHECK (expected_response_type = ANY (ARRAY['open_ended'::text, 'yes_no'::text, 'multiple_choice'::text, 'numeric'::text, 'scale'::text])),
  qualification_weight numeric DEFAULT 1.0 CHECK (qualification_weight >= 0::numeric AND qualification_weight <= 5::numeric),
  disqualification_indicators jsonb DEFAULT '[]'::jsonb,
  positive_indicators jsonb DEFAULT '[]'::jsonb,
  follow_up_questions jsonb DEFAULT '[]'::jsonb,
  objection_handling_notes text,
  sales_stage text CHECK (sales_stage = ANY (ARRAY['awareness'::text, 'consideration'::text, 'decision'::text, 'retention'::text])),
  priority_order integer,
  ai_reasoning text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_lead_qualification_framework_pkey PRIMARY KEY (id),
  CONSTRAINT ai_lead_qualification_framework_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_page_analysis (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  primary_page_purpose text NOT NULL,
  secondary_purposes jsonb DEFAULT '[]'::jsonb,
  page_type text CHECK (page_type = ANY (ARRAY['landing'::text, 'homepage'::text, 'product'::text, 'service'::text, 'about'::text, 'contact'::text, 'blog'::text, 'pricing'::text])),
  key_takeaways_for_leads jsonb NOT NULL DEFAULT '[]'::jsonb,
  value_proposition_clarity_score numeric CHECK (value_proposition_clarity_score >= 0::numeric AND value_proposition_clarity_score <= 10::numeric),
  hero_hook_effectiveness text NOT NULL,
  hero_clarity_score numeric CHECK (hero_clarity_score >= 0::numeric AND hero_clarity_score <= 10::numeric),
  hero_improvement_suggestions jsonb DEFAULT '[]'::jsonb,
  information_hierarchy_assessment text,
  content_flow_score numeric CHECK (content_flow_score >= 0::numeric AND content_flow_score <= 10::numeric),
  cognitive_load_assessment text,
  trust_signals_present jsonb DEFAULT '[]'::jsonb,
  credibility_gaps jsonb DEFAULT '[]'::jsonb,
  social_proof_effectiveness text,
  primary_cta_analysis text,
  cta_placement_effectiveness text,
  cta_optimization_suggestions jsonb DEFAULT '[]'::jsonb,
  ai_confidence_score numeric CHECK (ai_confidence_score >= 0::numeric AND ai_confidence_score <= 1::numeric),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_page_analysis_pkey PRIMARY KEY (id),
  CONSTRAINT ai_page_analysis_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_page_summaries (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  page_purpose text,
  key_takeaways jsonb DEFAULT '[]'::jsonb,
  hero_effectiveness text,
  value_proposition_clarity text,
  lead_capture_assessment text,
  user_journey_analysis text,
  business_impact_summary text,
  conversion_assessment text,
  trust_signals_analysis text,
  mobile_experience_notes text,
  competitive_advantages jsonb DEFAULT '[]'::jsonb,
  improvement_priorities jsonb DEFAULT '[]'::jsonb,
  business_model_insights text,
  target_audience_analysis text,
  content_strategy_notes text,
  ai_generated boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  target_audience_markdown text,
  business_type_description text,
  value_proposition_description text,
  business_impact_insights jsonb DEFAULT '[]'::jsonb,
  CONSTRAINT ai_page_summaries_pkey PRIMARY KEY (id),
  CONSTRAINT ai_page_summaries_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_prospect_concerns (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  concern_category text NOT NULL CHECK (concern_category = ANY (ARRAY['pricing'::text, 'trust'::text, 'functionality'::text, 'support'::text, 'implementation'::text, 'security'::text, 'scalability'::text])),
  concern_text text NOT NULL,
  concern_trigger text NOT NULL,
  concern_severity text CHECK (concern_severity = ANY (ARRAY['low'::text, 'medium'::text, 'high'::text, 'critical'::text])),
  target_audience_segment text,
  resolution_strategy text NOT NULL,
  prevention_recommendations text,
  supporting_evidence_needed jsonb DEFAULT '[]'::jsonb,
  competitive_context text,
  business_impact_if_unaddressed text,
  ai_confidence_level numeric CHECK (ai_confidence_level >= 0::numeric AND ai_confidence_level <= 1::numeric),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_prospect_concerns_pkey PRIMARY KEY (id),
  CONSTRAINT ai_prospect_concerns_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_seo_insights (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  meta_optimization_notes text,
  content_seo_assessment text,
  technical_seo_issues jsonb DEFAULT '[]'::jsonb,
  keyword_opportunities jsonb DEFAULT '[]'::jsonb,
  local_seo_recommendations text,
  schema_markup_suggestions jsonb DEFAULT '[]'::jsonb,
  internal_linking_analysis text,
  page_speed_seo_impact text,
  mobile_seo_assessment text,
  competitor_analysis_notes text,
  content_gap_analysis jsonb DEFAULT '[]'::jsonb,
  seo_priority_actions jsonb DEFAULT '[]'::jsonb,
  expected_traffic_impact text,
  ai_generated boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_seo_insights_pkey PRIMARY KEY (id),
  CONSTRAINT ai_seo_insights_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.analyses (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  url text NOT NULL,
  title text NOT NULL,
  score numeric NOT NULL CHECK (score >= 0::numeric AND score <= 10::numeric),
  conversion_rate numeric NOT NULL,
  pros jsonb NOT NULL,
  cons jsonb NOT NULL,
  recommendations jsonb NOT NULL,
  target_audience text NOT NULL,
  adaptations jsonb NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  screenshot_url text,
  screenshot_date timestamp with time zone,
  priority_issues_count integer DEFAULT 0,
  suggestions_count integer DEFAULT 0,
  performance_score integer CHECK (performance_score >= 0 AND performance_score <= 100),
  performance_grade text CHECK (performance_grade = ANY (ARRAY['A'::text, 'B'::text, 'C'::text, 'D'::text, 'F'::text])),
  lcp_score numeric,
  fid_score numeric,
  cls_score numeric,
  seo_score integer CHECK (seo_score >= 0 AND seo_score <= 100),
  lighthouse_data jsonb,
  seo_data jsonb,
  conversion_score_display numeric DEFAULT score,
  overall_rating numeric,
  generation_status text DEFAULT 'pending'::text CHECK (generation_status = ANY (ARRAY['pending'::text, 'in_progress'::text, 'completed'::text, 'failed'::text])),
  generation_started_at timestamp with time zone,
  generation_completed_at timestamp with time zone,
  current_generation_step text,
  total_generation_steps integer DEFAULT 8,
  completed_generation_steps integer DEFAULT 0,
  generation_error text,
  all_content_generated boolean DEFAULT false,
  pros_cons_generated boolean DEFAULT false,
  lead_insights_generated boolean DEFAULT false,
  performance_insights_generated boolean DEFAULT false,
  seo_insights_generated boolean DEFAULT false,
  chat_context_generated boolean DEFAULT false,
  page_summary_enhanced boolean DEFAULT false,
  suggestions_generated boolean DEFAULT false,
  current_step integer DEFAULT 0,
  is_completed boolean DEFAULT false,
  current_process integer DEFAULT 0,
  CONSTRAINT analyses_pkey PRIMARY KEY (id),
  CONSTRAINT analyses_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.analysis_permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  user_id uuid NOT NULL,
  package_type text NOT NULL,
  has_summary boolean DEFAULT true,
  has_screenshot boolean DEFAULT true,
  has_score boolean DEFAULT true,
  has_performance boolean DEFAULT false,
  has_suggestions boolean DEFAULT false,
  has_proscons boolean DEFAULT false,
  has_leadinsights boolean DEFAULT false,
  has_seo boolean DEFAULT false,
  chat_messages_remaining integer DEFAULT 0,
  reruns_remaining integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT analysis_permissions_pkey PRIMARY KEY (id),
  CONSTRAINT analysis_permissions_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT analysis_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.business_impact_analysis (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid,
  impact_category text NOT NULL,
  impact_description text NOT NULL,
  quantified_impact text,
  implementation_priority text CHECK (implementation_priority = ANY (ARRAY['High'::text, 'Medium'::text, 'Low'::text])),
  expected_timeline text,
  success_metrics jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT business_impact_analysis_pkey PRIMARY KEY (id),
  CONSTRAINT business_impact_analysis_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.contextual_guidance (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  guidance_key text NOT NULL UNIQUE,
  guidance_type text NOT NULL CHECK (guidance_type = ANY (ARRAY['onboarding'::text, 'feature_intro'::text, 'troubleshooting'::text, 'best_practice'::text])),
  title text NOT NULL,
  description text NOT NULL,
  steps jsonb DEFAULT '[]'::jsonb,
  prerequisites jsonb DEFAULT '[]'::jsonb,
  expected_outcome text,
  difficulty_level text CHECK (difficulty_level = ANY (ARRAY['beginner'::text, 'intermediate'::text, 'advanced'::text])),
  estimated_time_minutes integer,
  related_features jsonb DEFAULT '[]'::jsonb,
  video_url text,
  documentation_links jsonb DEFAULT '[]'::jsonb,
  is_active boolean DEFAULT true,
  display_order integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT contextual_guidance_pkey PRIMARY KEY (id)
);
CREATE TABLE public.lead_qualification_insights (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid,
  question_type text,
  question_text text,
  context_explanation text,
  qualification_value text,
  suggested_response text,
  priority_level integer,
  business_impact_score numeric CHECK (business_impact_score >= 0::numeric AND business_impact_score <= 10::numeric),
  conversion_relevance text,
  follow_up_questions jsonb DEFAULT '[]'::jsonb,
  objection_handling text,
  qualification_criteria text,
  lead_scoring_weight numeric DEFAULT 1.0,
  created_at timestamp with time zone DEFAULT now(),
  business_impact_description text,
  target_audience_markdown text,
  business_type_description text,
  value_proposition_description text,
  CONSTRAINT lead_qualification_insights_pkey PRIMARY KEY (id),
  CONSTRAINT lead_qualification_insights_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.lighthouse_accessibility_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  audit_run_id uuid NOT NULL,
  analysis_id uuid NOT NULL,
  accessibility_score integer CHECK (accessibility_score >= 0 AND accessibility_score <= 100),
  color_contrast_issues jsonb DEFAULT '[]'::jsonb,
  keyboard_navigation_issues jsonb DEFAULT '[]'::jsonb,
  aria_issues jsonb DEFAULT '[]'::jsonb,
  missing_alt_text_count integer DEFAULT 0,
  form_accessibility_issues jsonb DEFAULT '[]'::jsonb,
  focus_management_issues jsonb DEFAULT '[]'::jsonb,
  semantic_html_issues jsonb DEFAULT '[]'::jsonb,
  passed_audits jsonb DEFAULT '[]'::jsonb,
  failed_audits jsonb DEFAULT '[]'::jsonb,
  manual_audits jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT lighthouse_accessibility_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT lighthouse_accessibility_metrics_audit_run_id_fkey FOREIGN KEY (audit_run_id) REFERENCES public.lighthouse_audit_runs(id),
  CONSTRAINT lighthouse_accessibility_metrics_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.lighthouse_audit_runs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  audit_timestamp timestamp with time zone DEFAULT now(),
  lighthouse_version text,
  chrome_version text,
  audit_config jsonb DEFAULT '{}'::jsonb,
  device_type text DEFAULT 'mobile'::text CHECK (device_type = ANY (ARRAY['mobile'::text, 'desktop'::text])),
  network_throttling text,
  cpu_throttling numeric,
  audit_duration_ms numeric,
  audit_status text CHECK (audit_status = ANY (ARRAY['running'::text, 'completed'::text, 'failed'::text, 'timeout'::text])),
  error_message text,
  raw_lighthouse_json jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT lighthouse_audit_runs_pkey PRIMARY KEY (id),
  CONSTRAINT lighthouse_audit_runs_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.lighthouse_best_practices_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  audit_run_id uuid NOT NULL,
  analysis_id uuid NOT NULL,
  best_practices_score integer CHECK (best_practices_score >= 0 AND best_practices_score <= 100),
  https_usage boolean DEFAULT false,
  mixed_content_issues jsonb DEFAULT '[]'::jsonb,
  vulnerable_libraries jsonb DEFAULT '[]'::jsonb,
  deprecated_apis jsonb DEFAULT '[]'::jsonb,
  console_errors jsonb DEFAULT '[]'::jsonb,
  image_optimization_issues jsonb DEFAULT '[]'::jsonb,
  caching_issues jsonb DEFAULT '[]'::jsonb,
  doctype_present boolean DEFAULT true,
  charset_declared boolean DEFAULT true,
  passed_audits jsonb DEFAULT '[]'::jsonb,
  failed_audits jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT lighthouse_best_practices_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT lighthouse_best_practices_metrics_audit_run_id_fkey FOREIGN KEY (audit_run_id) REFERENCES public.lighthouse_audit_runs(id),
  CONSTRAINT lighthouse_best_practices_metrics_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.lighthouse_metrics_history (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  audit_run_id uuid NOT NULL,
  performance_score integer,
  accessibility_score integer,
  best_practices_score integer,
  seo_score integer,
  pwa_score integer,
  lcp_ms numeric,
  fid_ms numeric,
  cls_score numeric,
  performance_delta integer,
  accessibility_delta integer,
  best_practices_delta integer,
  seo_delta integer,
  overall_trend text CHECK (overall_trend = ANY (ARRAY['improving'::text, 'declining'::text, 'stable'::text])),
  significant_changes jsonb DEFAULT '[]'::jsonb,
  audit_date timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT lighthouse_metrics_history_pkey PRIMARY KEY (id),
  CONSTRAINT lighthouse_metrics_history_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT lighthouse_metrics_history_audit_run_id_fkey FOREIGN KEY (audit_run_id) REFERENCES public.lighthouse_audit_runs(id)
);
CREATE TABLE public.lighthouse_performance_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  audit_run_id uuid NOT NULL,
  analysis_id uuid NOT NULL,
  largest_contentful_paint_ms numeric,
  first_input_delay_ms numeric,
  cumulative_layout_shift numeric,
  first_contentful_paint_ms numeric,
  speed_index_ms numeric,
  time_to_interactive_ms numeric,
  first_meaningful_paint_ms numeric,
  total_blocking_time_ms numeric,
  max_potential_fid_ms numeric,
  layout_shift_events jsonb DEFAULT '[]'::jsonb,
  total_byte_weight integer,
  dom_size integer,
  critical_request_chains jsonb DEFAULT '[]'::jsonb,
  performance_score integer CHECK (performance_score >= 0 AND performance_score <= 100),
  opportunities jsonb DEFAULT '[]'::jsonb,
  diagnostics jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  time_to_first_byte_ms numeric,
  server_response_time_ms numeric,
  render_blocking_resources jsonb DEFAULT '[]'::jsonb,
  unused_css_rules jsonb DEFAULT '[]'::jsonb,
  image_optimization_opportunities jsonb DEFAULT '[]'::jsonb,
  network_requests_count integer DEFAULT 0,
  total_transfer_size_bytes bigint DEFAULT 0,
  main_thread_work_breakdown jsonb DEFAULT '{}'::jsonb,
  third_party_summary jsonb DEFAULT '{}'::jsonb,
  resource_summary jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT lighthouse_performance_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT lighthouse_performance_metrics_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT lighthouse_performance_metrics_audit_run_id_fkey FOREIGN KEY (audit_run_id) REFERENCES public.lighthouse_audit_runs(id)
);
CREATE TABLE public.lighthouse_pwa_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  audit_run_id uuid NOT NULL,
  analysis_id uuid NOT NULL,
  pwa_score integer CHECK (pwa_score >= 0 AND pwa_score <= 100),
  installable boolean DEFAULT false,
  manifest_present boolean DEFAULT false,
  manifest_valid boolean DEFAULT false,
  service_worker_present boolean DEFAULT false,
  service_worker_valid boolean DEFAULT false,
  offline_support boolean DEFAULT false,
  app_shell_present boolean DEFAULT false,
  splash_screen_configured boolean DEFAULT false,
  theme_color_configured boolean DEFAULT false,
  passed_audits jsonb DEFAULT '[]'::jsonb,
  failed_audits jsonb DEFAULT '[]'::jsonb,
  manual_audits jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT lighthouse_pwa_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT lighthouse_pwa_metrics_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT lighthouse_pwa_metrics_audit_run_id_fkey FOREIGN KEY (audit_run_id) REFERENCES public.lighthouse_audit_runs(id)
);
CREATE TABLE public.lighthouse_seo_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  audit_run_id uuid NOT NULL,
  analysis_id uuid NOT NULL,
  seo_score integer CHECK (seo_score >= 0 AND seo_score <= 100),
  title_tag_present boolean DEFAULT false,
  title_tag_length integer,
  meta_description_present boolean DEFAULT false,
  meta_description_length integer,
  h1_count integer DEFAULT 0,
  heading_structure_valid boolean DEFAULT true,
  robots_txt_valid boolean DEFAULT true,
  canonical_url_present boolean DEFAULT false,
  viewport_meta_present boolean DEFAULT false,
  mobile_friendly boolean DEFAULT true,
  page_load_impact_on_seo text,
  structured_data_present boolean DEFAULT false,
  structured_data_valid boolean DEFAULT true,
  structured_data_types jsonb DEFAULT '[]'::jsonb,
  images_missing_alt_count integer DEFAULT 0,
  internal_links_count integer DEFAULT 0,
  external_links_count integer DEFAULT 0,
  passed_audits jsonb DEFAULT '[]'::jsonb,
  failed_audits jsonb DEFAULT '[]'::jsonb,
  manual_audits jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT lighthouse_seo_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT lighthouse_seo_metrics_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT lighthouse_seo_metrics_audit_run_id_fkey FOREIGN KEY (audit_run_id) REFERENCES public.lighthouse_audit_runs(id)
);
CREATE TABLE public.messages (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  user_id uuid NOT NULL,
  content text NOT NULL,
  role text NOT NULL CHECK (role = ANY (ARRAY['user'::text, 'assistant'::text])),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT messages_pkey PRIMARY KEY (id),
  CONSTRAINT messages_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT messages_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.performance_chart_data (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid,
  chart_type text NOT NULL CHECK (chart_type = ANY (ARRAY['breakdown'::text, 'timeline'::text, 'comparison'::text])),
  chart_data jsonb NOT NULL DEFAULT '{}'::jsonb,
  chart_config jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT performance_chart_data_pkey PRIMARY KEY (id),
  CONSTRAINT performance_chart_data_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.performance_impact_insights (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  impact_type text NOT NULL CHECK (impact_type = ANY (ARRAY['critical'::text, 'opportunity'::text, 'optimization'::text])),
  metric_name text NOT NULL,
  current_value numeric,
  target_value numeric,
  impact_description text NOT NULL,
  conversion_impact text,
  implementation_guide text,
  expected_improvement text,
  effort_level text CHECK (effort_level = ANY (ARRAY['Low'::text, 'Medium'::text, 'High'::text])),
  timeline text,
  core_web_vitals_impact text,
  mobile_performance_notes text,
  lighthouse_recommendations jsonb DEFAULT '[]'::jsonb,
  conversion_correlation text,
  technical_debt_assessment text,
  performance_budget_notes text,
  monitoring_recommendations text,
  created_at timestamp with time zone DEFAULT now(),
  ai_assessment_score numeric CHECK (ai_assessment_score >= 0::numeric AND ai_assessment_score <= 10::numeric),
  color_rating text CHECK (color_rating = ANY (ARRAY['red'::text, 'yellow'::text, 'green'::text])),
  metric_category text DEFAULT 'general'::text,
  CONSTRAINT performance_impact_insights_pkey PRIMARY KEY (id),
  CONSTRAINT performance_impact_insights_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.performance_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  metric_name text NOT NULL,
  metric_value numeric NOT NULL,
  metric_unit text,
  metric_category text,
  is_good boolean,
  threshold_good numeric,
  threshold_poor numeric,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT performance_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT performance_metrics_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.profiles (
  id uuid NOT NULL,
  email text NOT NULL,
  display_name text,
  avatar_url text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id)
);
CREATE TABLE public.progress_subscriptions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  analysis_id uuid NOT NULL,
  session_id text NOT NULL,
  connection_id text NOT NULL,
  subscribed_at timestamp with time zone DEFAULT now(),
  last_ping timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  client_info jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT progress_subscriptions_pkey PRIMARY KEY (id),
  CONSTRAINT progress_subscriptions_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT progress_subscriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.seo_issues (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  issue text NOT NULL,
  recommendation text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  severity_score integer NOT NULL CHECK (severity_score >= 1 AND severity_score <= 5),
  issue_type text DEFAULT 'general'::text,
  fix_instructions text,
  impact_description text,
  CONSTRAINT seo_issues_pkey PRIMARY KEY (id),
  CONSTRAINT seo_issues_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.seo_scoring_details (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid,
  overall_score numeric CHECK (overall_score >= 0::numeric AND overall_score <= 100::numeric),
  technical_score numeric CHECK (technical_score >= 0::numeric AND technical_score <= 100::numeric),
  content_score numeric CHECK (content_score >= 0::numeric AND content_score <= 100::numeric),
  performance_impact_score numeric CHECK (performance_impact_score >= 0::numeric AND performance_impact_score <= 100::numeric),
  scoring_breakdown jsonb DEFAULT '{}'::jsonb,
  improvement_recommendations jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT seo_scoring_details_pkey PRIMARY KEY (id),
  CONSTRAINT seo_scoring_details_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.stripe_customers (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  user_id uuid NOT NULL UNIQUE,
  customer_id text NOT NULL UNIQUE,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  deleted_at timestamp with time zone,
  CONSTRAINT stripe_customers_pkey PRIMARY KEY (id),
  CONSTRAINT stripe_customers_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.stripe_orders (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  checkout_session_id text NOT NULL,
  payment_intent_id text NOT NULL,
  customer_id text NOT NULL,
  amount_subtotal bigint NOT NULL,
  amount_total bigint NOT NULL,
  currency text NOT NULL,
  payment_status text NOT NULL,
  status USER-DEFINED NOT NULL DEFAULT 'pending'::stripe_order_status,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  deleted_at timestamp with time zone,
  CONSTRAINT stripe_orders_pkey PRIMARY KEY (id)
);
CREATE TABLE public.stripe_subscriptions (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  customer_id text NOT NULL UNIQUE,
  subscription_id text,
  price_id text,
  current_period_start bigint,
  current_period_end bigint,
  cancel_at_period_end boolean DEFAULT false,
  payment_method_brand text,
  payment_method_last4 text,
  status USER-DEFINED NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  deleted_at timestamp with time zone,
  CONSTRAINT stripe_subscriptions_pkey PRIMARY KEY (id)
);
CREATE TABLE public.suggestion_categories (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  icon text,
  color text,
  description text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT suggestion_categories_pkey PRIMARY KEY (id)
);
CREATE TABLE public.suggestion_headers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  suggestion_id uuid,
  ai_generated_title text NOT NULL,
  original_title text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT suggestion_headers_pkey PRIMARY KEY (id),
  CONSTRAINT suggestion_headers_suggestion_id_fkey FOREIGN KEY (suggestion_id) REFERENCES public.suggestions(id)
);
CREATE TABLE public.suggestions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  category text NOT NULL,
  title text NOT NULL,
  description text NOT NULL,
  detailed_explanation text,
  impact_level text NOT NULL CHECK (impact_level = ANY (ARRAY['High'::text, 'Medium'::text, 'Low'::text, 'high'::text, 'medium'::text, 'low'::text, 'HIGH'::text, 'MEDIUM'::text, 'LOW'::text, 'Critical'::text, 'Important'::text, 'Minor'::text, 'critical'::text, 'important'::text, 'minor'::text])),
  effort_level text NOT NULL CHECK (effort_level = ANY (ARRAY['High'::text, 'Medium'::text, 'Low'::text, 'high'::text, 'medium'::text, 'low'::text, 'HIGH'::text, 'MEDIUM'::text, 'LOW'::text, 'Complex'::text, 'Moderate'::text, 'Simple'::text, 'complex'::text, 'moderate'::text, 'simple'::text])),
  priority integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  implementation_steps text,
  expected_impact text,
  business_value text,
  CONSTRAINT suggestions_pkey PRIMARY KEY (id),
  CONSTRAINT suggestions_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ui_info_content (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  content_key text NOT NULL UNIQUE,
  content_type text NOT NULL CHECK (content_type = ANY (ARRAY['tooltip'::text, 'help_text'::text, 'modal_content'::text, 'guide_step'::text])),
  title text,
  content_text text NOT NULL,
  content_html text,
  icon_name text,
  position_hint text CHECK (position_hint = ANY (ARRAY['top'::text, 'bottom'::text, 'left'::text, 'right'::text, 'center'::text])),
  trigger_event text CHECK (trigger_event = ANY (ARRAY['hover'::text, 'click'::text, 'focus'::text, 'auto'::text])),
  display_duration_ms integer,
  target_audience text CHECK (target_audience = ANY (ARRAY['beginner'::text, 'intermediate'::text, 'advanced'::text, 'all'::text])),
  page_context text,
  component_context text,
  is_active boolean DEFAULT true,
  display_order integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ui_info_content_pkey PRIMARY KEY (id)
);
CREATE TABLE public.ui_interaction_tracking (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  session_id text NOT NULL,
  interaction_type text NOT NULL CHECK (interaction_type = ANY (ARRAY['tooltip_view'::text, 'help_click'::text, 'guide_step'::text, 'tab_switch'::text, 'modal_open'::text])),
  content_key text,
  page_path text NOT NULL,
  component_name text,
  interaction_timestamp timestamp with time zone DEFAULT now(),
  interaction_duration_ms integer,
  user_agent text,
  viewport_size text,
  was_helpful boolean,
  feedback_text text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ui_interaction_tracking_pkey PRIMARY KEY (id),
  CONSTRAINT ui_interaction_tracking_content_key_fkey FOREIGN KEY (content_key) REFERENCES public.ui_info_content(content_key),
  CONSTRAINT ui_interaction_tracking_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.user_addons (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  analysis_id uuid,
  addon_type text NOT NULL,
  purchase_id uuid NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_addons_pkey PRIMARY KEY (id),
  CONSTRAINT user_addons_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id),
  CONSTRAINT user_addons_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT user_addons_purchase_id_fkey FOREIGN KEY (purchase_id) REFERENCES public.user_purchases(id)
);
CREATE TABLE public.user_purchases (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  product_id text NOT NULL,
  price_id text NOT NULL,
  package_type text,
  category text NOT NULL,
  analysis_credits integer DEFAULT 0,
  chat_messages integer DEFAULT 0,
  reruns integer DEFAULT 0,
  stripe_payment_intent_id text,
  amount_paid numeric NOT NULL,
  currency text DEFAULT 'usd'::text,
  purchase_date timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  expires_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_purchases_pkey PRIMARY KEY (id),
  CONSTRAINT user_purchases_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.website_content_analysis (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  raw_html_content text,
  cleaned_content text,
  extracted_text text,
  page_structure jsonb DEFAULT '{}'::jsonb,
  semantic_elements jsonb DEFAULT '{}'::jsonb,
  conversion_elements jsonb DEFAULT '{}'::jsonb,
  business_indicators jsonb DEFAULT '{}'::jsonb,
  content_quality_metrics jsonb DEFAULT '{}'::jsonb,
  extraction_method text DEFAULT 'puppeteer'::text,
  extraction_timestamp timestamp with time zone DEFAULT now(),
  content_hash text,
  word_count integer,
  readability_score numeric,
  sentiment_analysis jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT website_content_analysis_pkey PRIMARY KEY (id),
  CONSTRAINT website_content_analysis_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);