<script setup lang="ts">
import { ref } from 'vue';
import ProcessCompletionAnimation from './ProcessCompletionAnimation.vue';

const processRef = ref();

// Example process steps for ConvertIQ analysis
const analysisSteps = ref([
  {
    id: 'fetch-page',
    title: 'Fetching Page Content',
    description: 'Downloading and parsing the landing page HTML',
    status: 'pending' as const,
    duration: 2000
  },
  {
    id: 'performance-analysis',
    title: 'Performance Analysis',
    description: 'Running Lighthouse performance tests',
    status: 'pending' as const,
    duration: 3000
  },
  {
    id: 'seo-analysis',
    title: 'SEO Analysis',
    description: 'Analyzing meta tags, headings, and content structure',
    status: 'pending' as const,
    duration: 1500
  },
  {
    id: 'ai-insights',
    title: 'AI Insights Generation',
    description: 'Generating conversion optimization recommendations',
    status: 'pending' as const,
    duration: 2500
  },
  {
    id: 'lead-scoring',
    title: 'Lead Scoring Analysis',
    description: 'Calculating conversion potential and lead quality metrics',
    status: 'pending' as const,
    duration: 1800
  },
  {
    id: 'final-report',
    title: 'Generating Report',
    description: 'Compiling final analysis with actionable insights',
    status: 'pending' as const,
    duration: 1200
  }
]);

const onProcessComplete = () => {
  console.log('Analysis process completed!');
  // Here you could redirect to results page or show completion message
};

const onStepComplete = (stepId: string) => {
  console.log(`Step completed: ${stepId}`);
  // Here you could update UI or send analytics events
};

const startDemo = () => {
  processRef.value?.startProcess();
};

const resetDemo = () => {
  processRef.value?.resetProcess();
};
</script>

<template>
  <div class="max-w-4xl mx-auto p-6">
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">
        Process Completion Animation Demo
      </h2>
      <p class="text-gray-600">
        This demonstrates the animated progress indicator for ConvertIQ analysis tasks.
        The animation follows the 300-500ms transition patterns used throughout the application.
      </p>
    </div>

    <!-- Demo Controls -->
    <div class="mb-8 p-4 bg-gray-50 rounded-lg">
      <h3 class="text-lg font-semibold text-gray-900 mb-3">Demo Controls</h3>
      <div class="flex space-x-3">
        <button
          @click="startDemo"
          class="btn-primary"
        >
          Start Analysis Demo
        </button>
        <button
          @click="resetDemo"
          class="btn-secondary"
        >
          Reset Demo
        </button>
      </div>
    </div>

    <!-- Process Animation -->
    <div class="bg-white rounded-lg shadow-lg p-6">
      <ProcessCompletionAnimation
        ref="processRef"
        :steps="analysisSteps"
        :auto-start="false"
        @complete="onProcessComplete"
        @step-complete="onStepComplete"
      />
    </div>

    <!-- Usage Instructions -->
    <div class="mt-8 p-6 bg-blue-50 rounded-lg">
      <h3 class="text-lg font-semibold text-blue-900 mb-3">Usage Instructions</h3>
      <div class="text-blue-800 space-y-2">
        <p><strong>Integration:</strong> Import and use ProcessCompletionAnimation in any component that needs to show task progress.</p>
        <p><strong>Customization:</strong> Define your own steps array with id, title, description, and duration.</p>
        <p><strong>Events:</strong> Listen for 'complete' and 'step-complete' events to handle process completion.</p>
        <p><strong>Control:</strong> Use the exposed methods startProcess() and resetProcess() to control the animation.</p>
      </div>
    </div>

    <!-- Code Example -->
    <div class="mt-8">
      <h3 class="text-lg font-semibold text-gray-900 mb-3">Code Example</h3>
      <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm"><code>// Define your process steps
const steps = [
  {
    id: 'step-1',
    title: 'Step Title',
    description: 'Step description',
    status: 'pending',
    duration: 2000 // milliseconds
  }
];

// Use in template
&lt;ProcessCompletionAnimation
  :steps="steps"
  :auto-start="false"
  @complete="onComplete"
  @step-complete="onStepComplete"
/&gt;</code></pre>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.btn-secondary {
  @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}
</style>
