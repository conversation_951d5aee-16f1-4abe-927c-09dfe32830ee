import { supabase } from './supabase';
import type { UserBalance, UserPurchase, AnalysisPackage } from '../types/billing';

export class BillingService {
  static async getUserBalance(userId: string): Promise<UserBalance> {
    try {
      const { data: purchases, error } = await supabase
        .from('user_purchases')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) throw error;

      // Calculate total credits from all active purchases
      const totalCredits = purchases?.reduce((sum, purchase) => sum + purchase.analysis_credits, 0) || 0;
      const totalChatMessages = purchases?.reduce((sum, purchase) => sum + purchase.chat_messages, 0) || 0;
      const totalReruns = purchases?.reduce((sum, purchase) => sum + purchase.reruns, 0) || 0;

      return {
        analysis_credits: totalCredits,
        chat_messages: totalChatMessages,
        reruns: totalReruns,
        purchased_packages: purchases || []
      };
    } catch (error) {
      console.error('Error fetching user balance:', error);
      throw error;
    }
  }

  static async getAnalysisPermissions(userId: string, analysisId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('analysis_permissions')
        .select('*')
        .eq('user_id', userId)
        .eq('analysis_id', analysisId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      return data || {
        has_summary: true,
        has_screenshot: true,
        has_score: true,
        has_performance: false,
        has_suggestions: false,
        has_proscons: false,
        has_leadinsights: false,
        has_seo: false,
        chat_messages_remaining: 0,
        reruns_remaining: 0
      };
    } catch (error) {
      console.error('Error fetching analysis permissions:', error);
      throw error;
    }
  }

  static async consumeCredit(userId: string, creditType: 'analysis' | 'chat' | 'rerun'): Promise<boolean> {
    try {
      const response = await fetch('/api/billing/consume-credit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          creditType
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to consume credit');
      }

      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Error consuming credit:', error);
      return false;
    }
  }

  static async getAvailablePackages(): Promise<AnalysisPackage[]> {
    // This would typically come from your database or Stripe
    return [
      {
        id: 'basic',
        name: 'Basic Analysis',
        description: 'Essential insights for your landing page',
        price: 9.99,
        features: {
          has_summary: true,
          has_screenshot: true,
          has_score: true,
          has_performance: false,
          has_suggestions: false,
          has_proscons: false,
          has_leadinsights: false,
          has_seo: false,
          chat_messages: 5,
          reruns: 1
        },
        stripe_price_id: 'price_basic_analysis'
      },
      {
        id: 'professional',
        name: 'Professional Analysis',
        description: 'Comprehensive analysis with AI insights',
        price: 29.99,
        features: {
          has_summary: true,
          has_screenshot: true,
          has_score: true,
          has_performance: true,
          has_suggestions: true,
          has_proscons: true,
          has_leadinsights: false,
          has_seo: true,
          chat_messages: 20,
          reruns: 3
        },
        stripe_price_id: 'price_professional_analysis'
      },
      {
        id: 'premium',
        name: 'Premium Analysis',
        description: 'Complete analysis suite with lead insights',
        price: 49.99,
        features: {
          has_summary: true,
          has_screenshot: true,
          has_score: true,
          has_performance: true,
          has_suggestions: true,
          has_proscons: true,
          has_leadinsights: true,
          has_seo: true,
          chat_messages: 50,
          reruns: 5
        },
        stripe_price_id: 'price_premium_analysis'
      }
    ];
  }
}