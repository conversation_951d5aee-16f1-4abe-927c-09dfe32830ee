<script setup lang="ts">
import { computed } from 'vue';
import { MessageCircle, CreditCard, ArrowRight, Zap } from 'lucide-vue-next';

const props = defineProps<{
  remainingMessages: number;
  isLimitReached: boolean;
}>();

const limitStatus = computed(() => {
  if (props.isLimitReached) {
    return {
      color: 'bg-red-50 border-red-200 text-red-800',
      icon: 'text-red-500',
      title: 'Chat Limit Reached',
      message: 'You\'ve used all your chat messages for this month.'
    };
  } else if (props.remainingMessages <= 5) {
    return {
      color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      icon: 'text-yellow-500',
      title: 'Running Low on Messages',
      message: `Only ${props.remainingMessages} messages remaining.`
    };
  } else {
    return {
      color: 'bg-blue-50 border-blue-200 text-blue-800',
      icon: 'text-blue-500',
      title: 'Chat Available',
      message: `${props.remainingMessages} messages remaining.`
    };
  }
});
</script>

<template>
  <div class="rounded-lg border p-4 mb-4" :class="limitStatus.color">
    <div class="flex items-start justify-between">
      <div class="flex items-start">
        <MessageCircle :class="limitStatus.icon" class="w-5 h-5 mr-3 mt-0.5" />
        <div>
          <h4 class="font-medium mb-1">{{ limitStatus.title }}</h4>
          <p class="text-sm">{{ limitStatus.message }}</p>
        </div>
      </div>
      
      <div v-if="isLimitReached || remainingMessages <= 5" class="ml-4">
        <a 
          href="/dashboard/billing"
          class="inline-flex items-center px-3 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
        >
          <CreditCard class="w-4 h-4 mr-2" />
          Buy More
        </a>
      </div>
    </div>

    <!-- Upgrade Prompt for Limit Reached -->
    <div v-if="isLimitReached" class="mt-4 pt-4 border-t border-red-200">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <Zap class="w-4 h-4 text-red-500 mr-2" />
          <span class="text-sm font-medium">Need more messages?</span>
        </div>
        <a 
          href="/dashboard/billing"
          class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
        >
          <span>Upgrade Now</span>
          <ArrowRight class="w-4 h-4 ml-2" />
        </a>
      </div>
    </div>
  </div>
</template>