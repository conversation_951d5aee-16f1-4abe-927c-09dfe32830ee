import { loadStripe } from '@stripe/stripe-js';

const stripePublishableKey = import.meta.env.PUBLIC_STRIPE_PUBLISHABLE_KEY;

if (!stripePublishableKey) {
  throw new Error('Missing PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable');
}

export const stripe = await loadStripe(stripePublishableKey);

export interface CheckoutSessionData {
  productId: string;
  quantity?: number;
  successUrl?: string;
  cancelUrl?: string;
}

export async function createCheckoutSession(data: CheckoutSessionData) {
  const response = await fetch('/api/stripe/create-checkout-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create checkout session');
  }

  return response.json();
}

export async function redirectToCheckout(sessionId: string) {
  if (!stripe) {
    throw new Error('Stripe not loaded');
  }

  const { error } = await stripe.redirectToCheckout({
    sessionId,
  });

  if (error) {
    throw error;
  }
}