<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { supabase } from '../../lib/supabase';
import { BillingService } from '../../lib/billing-service';
import { createCheckoutSession, redirectToCheckout } from '../../lib/stripe-client';
import type { UserBalance, AnalysisPackage } from '../../types/billing';
import { 
  CreditCard, 
  Package, 
  MessageCircle, 
  RefreshCw, 
  TrendingUp,
  Zap,
  Crown,
  ShoppingCart,
  CheckCircle,
  Clock
} from 'lucide-vue-next';

const userBalance = ref<UserBalance | null>(null);
const availablePackages = ref<AnalysisPackage[]>([]);
const loading = ref(true);
const purchasing = ref<string | null>(null);
const error = ref<string | null>(null);

onMounted(async () => {
  await loadBillingData();
});

const loadBillingData = async () => {
  try {
    loading.value = true;
    error.value = null;

    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) throw new Error('User not authenticated');

    // Load user balance
    const response = await fetch(`/api/billing/user-balance?userId=${userData.user.id}`);
    if (!response.ok) throw new Error('Failed to load balance');
    
    userBalance.value = await response.json();

    // Load available packages
    availablePackages.value = await BillingService.getAvailablePackages();

  } catch (err) {
    console.error('Error loading billing data:', err);
    error.value = err instanceof Error ? err.message : 'Failed to load billing data';
  } finally {
    loading.value = false;
  }
};

const purchasePackage = async (packageId: string) => {
  try {
    purchasing.value = packageId;
    
    const selectedPackage = availablePackages.value.find(p => p.id === packageId);
    if (!selectedPackage) throw new Error('Package not found');

    const { sessionId } = await createCheckoutSession({
      productId: packageId,
      quantity: 1,
      successUrl: `${window.location.origin}/dashboard/billing?success=true`,
      cancelUrl: `${window.location.origin}/dashboard/billing?canceled=true`
    });

    await redirectToCheckout(sessionId);

  } catch (err) {
    console.error('Error purchasing package:', err);
    error.value = err instanceof Error ? err.message : 'Failed to initiate purchase';
  } finally {
    purchasing.value = null;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getPackageIcon = (packageId: string) => {
  switch (packageId) {
    case 'basic': return Package;
    case 'professional': return Zap;
    case 'premium': return Crown;
    default: return Package;
  }
};

const getPackageColor = (packageId: string) => {
  switch (packageId) {
    case 'basic': return 'bg-blue-50 border-blue-200 text-blue-800';
    case 'professional': return 'bg-purple-50 border-purple-200 text-purple-800';
    case 'premium': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    default: return 'bg-gray-50 border-gray-200 text-gray-800';
  }
};
</script>

<template>
  <div class="max-w-7xl mx-auto px-6 py-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-2xl font-semibold text-gray-900 mb-2">Billing & Credits</h1>
      <p class="text-gray-600">Manage your analysis credits and purchase additional packages</p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div v-for="i in 3" :key="i" class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="animate-pulse">
            <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
            <div class="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div>
          <h3 class="text-red-800 font-medium">Error Loading Billing Data</h3>
          <p class="text-red-700 mt-1">{{ error }}</p>
        </div>
      </div>
      <button @click="loadBillingData" class="mt-4 btn-primary">
        <RefreshCw class="w-4 h-4 mr-2" />
        Retry
      </button>
    </div>

    <!-- Billing Content -->
    <div v-else class="space-y-8">
      <!-- Current Balance -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900">Current Balance</h2>
          <button @click="loadBillingData" class="btn-secondary text-sm">
            <RefreshCw class="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <BarChart3 class="w-6 h-6 text-blue-600" />
            </div>
            <div class="text-2xl font-bold text-blue-900">{{ userBalance?.analysis_credits || 0 }}</div>
            <div class="text-sm text-blue-700">Analysis Credits</div>
          </div>

          <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <MessageCircle class="w-6 h-6 text-green-600" />
            </div>
            <div class="text-2xl font-bold text-green-900">{{ userBalance?.chat_messages || 0 }}</div>
            <div class="text-sm text-green-700">Chat Messages</div>
          </div>

          <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <RefreshCw class="w-6 h-6 text-purple-600" />
            </div>
            <div class="text-2xl font-bold text-purple-900">{{ userBalance?.reruns || 0 }}</div>
            <div class="text-sm text-purple-700">Reruns Available</div>
          </div>
        </div>
      </div>

      <!-- Available Packages -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">Available Analysis Packages</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div 
            v-for="pkg in availablePackages" 
            :key="pkg.id"
            class="border border-gray-200 rounded-lg p-6 relative"
            :class="pkg.id === 'professional' ? 'border-blue-500 ring-2 ring-blue-100' : ''"
          >
            <!-- Popular Badge -->
            <div v-if="pkg.id === 'professional'" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                Most Popular
              </span>
            </div>

            <div class="text-center mb-6">
              <div class="w-12 h-12 mx-auto mb-4 rounded-lg flex items-center justify-center"
                   :class="getPackageColor(pkg.id)">
                <component :is="getPackageIcon(pkg.id)" class="w-6 h-6" />
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-2">{{ pkg.name }}</h3>
              <p class="text-gray-600 mb-4">{{ pkg.description }}</p>
              <div class="text-3xl font-bold text-gray-900">${{ pkg.price }}</div>
            </div>

            <!-- Features -->
            <div class="space-y-3 mb-6">
              <div class="flex items-center text-sm">
                <CheckCircle class="w-4 h-4 text-green-500 mr-2" />
                <span>Page Summary & Screenshot</span>
              </div>
              <div class="flex items-center text-sm">
                <CheckCircle class="w-4 h-4 text-green-500 mr-2" />
                <span>Conversion Score</span>
              </div>
              <div class="flex items-center text-sm" :class="pkg.features.has_performance ? '' : 'opacity-50'">
                <CheckCircle v-if="pkg.features.has_performance" class="w-4 h-4 text-green-500 mr-2" />
                <Clock v-else class="w-4 h-4 text-gray-400 mr-2" />
                <span>Performance Analysis</span>
              </div>
              <div class="flex items-center text-sm" :class="pkg.features.has_suggestions ? '' : 'opacity-50'">
                <CheckCircle v-if="pkg.features.has_suggestions" class="w-4 h-4 text-green-500 mr-2" />
                <Clock v-else class="w-4 h-4 text-gray-400 mr-2" />
                <span>AI Suggestions</span>
              </div>
              <div class="flex items-center text-sm" :class="pkg.features.has_leadinsights ? '' : 'opacity-50'">
                <CheckCircle v-if="pkg.features.has_leadinsights" class="w-4 h-4 text-green-500 mr-2" />
                <Clock v-else class="w-4 h-4 text-gray-400 mr-2" />
                <span>Lead Insights</span>
              </div>
              <div class="flex items-center text-sm">
                <MessageCircle class="w-4 h-4 text-blue-500 mr-2" />
                <span>{{ pkg.features.chat_messages }} Chat Messages</span>
              </div>
              <div class="flex items-center text-sm">
                <RefreshCw class="w-4 h-4 text-purple-500 mr-2" />
                <span>{{ pkg.features.reruns }} Reruns</span>
              </div>
            </div>

            <!-- Purchase Button -->
            <button
              @click="purchasePackage(pkg.id)"
              :disabled="purchasing === pkg.id"
              class="w-full btn-primary"
              :class="pkg.id === 'professional' ? 'bg-blue-600 hover:bg-blue-700' : ''"
            >
              <ShoppingCart class="w-4 h-4 mr-2" />
              <span v-if="purchasing === pkg.id">Processing...</span>
              <span v-else>Purchase Package</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Purchase History -->
      <div v-if="userBalance?.purchased_packages.length" class="bg-white rounded-lg border border-gray-200 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">Purchase History</h2>
        
        <div class="space-y-4">
          <div 
            v-for="purchase in userBalance.purchased_packages" 
            :key="purchase.id"
            class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200"
          >
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Package class="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 class="font-medium text-gray-900">{{ purchase.package_type }}</h3>
                <p class="text-sm text-gray-600">Purchased {{ formatDate(purchase.purchase_date) }}</p>
              </div>
            </div>
            <div class="text-right">
              <div class="text-lg font-bold text-gray-900">${{ purchase.amount_paid }}</div>
              <div class="text-sm text-gray-600">
                {{ purchase.analysis_credits }} credits, {{ purchase.chat_messages }} messages
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Usage Statistics -->
      <div v-if="userBalance?.usage_stats" class="bg-white rounded-lg border border-gray-200 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">Usage Statistics</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <TrendingUp class="w-8 h-8 text-gray-600 mx-auto mb-2" />
            <div class="text-2xl font-bold text-gray-900">{{ userBalance.usage_stats.total_analyses }}</div>
            <div class="text-sm text-gray-600">Total Analyses</div>
          </div>
          
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <MessageCircle class="w-8 h-8 text-gray-600 mx-auto mb-2" />
            <div class="text-2xl font-bold text-gray-900">{{ userBalance.usage_stats.total_chat_messages }}</div>
            <div class="text-sm text-gray-600">Chat Messages Sent</div>
          </div>
          
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <Clock class="w-8 h-8 text-gray-600 mx-auto mb-2" />
            <div class="text-2xl font-bold text-gray-900">{{ userBalance.usage_stats.this_month_analyses }}</div>
            <div class="text-sm text-gray-600">This Month</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}
</style>