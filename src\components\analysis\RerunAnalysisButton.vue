<script setup lang="ts">
import { ref, computed } from 'vue';
import { supabase } from '../../lib/supabase';
import { BillingService } from '../../lib/billing-service';
import { RefreshCw, AlertTriangle, CheckCircle } from 'lucide-vue-next';

const props = defineProps<{
  analysisId: string;
  websiteUrl: string;
  currentRerunNumber?: number;
}>();

const isRerunning = ref(false);
const error = ref<string | null>(null);
const success = ref<string | null>(null);

const rerunAnalysis = async () => {
  try {
    isRerunning.value = true;
    error.value = null;
    success.value = null;

    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) throw new Error('User not authenticated');

    // Check if user has rerun credits
    const canConsume = await BillingService.consumeCredit(userData.user.id, 'rerun');
    if (!canConsume) {
      error.value = 'No rerun credits available. Please purchase more credits.';
      return;
    }

    // Create new analysis record for rerun
    const rerunNumber = (props.currentRerunNumber || 0) + 1;
    const rerunTitle = `${new URL(props.websiteUrl).hostname} rerun ${rerunNumber}`;

    const { data: newAnalysis, error: createError } = await supabase
      .from('analyses')
      .insert({
        user_id: userData.user.id,
        url: props.websiteUrl,
        title: rerunTitle,
        score: 0,
        conversion_rate: 0,
        pros: [],
        cons: [],
        recommendations: [],
        target_audience: 'General web users',
        adaptations: [],
        generation_status: 'pending'
      })
      .select('id')
      .single();

    if (createError) throw createError;

    // Start comprehensive analysis
    const analysisResponse = await fetch('/api/comprehensive-analysis', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: props.websiteUrl,
        analysisId: newAnalysis.id
      })
    });

    if (!analysisResponse.ok) {
      throw new Error('Failed to start rerun analysis');
    }

    success.value = 'Rerun started successfully!';
    
    // Redirect to new analysis
    setTimeout(() => {
      window.location.href = `/dashboard/analysis/${newAnalysis.id}`;
    }, 1500);

  } catch (err) {
    console.error('Error rerunning analysis:', err);
    error.value = err instanceof Error ? err.message : 'Failed to rerun analysis';
  } finally {
    isRerunning.value = false;
  }
};

const nextRerunTitle = computed(() => {
  const rerunNumber = (props.currentRerunNumber || 0) + 1;
  return `${new URL(props.websiteUrl).hostname} rerun ${rerunNumber}`;
});
</script>

<template>
  <div class="space-y-4">
    <!-- Rerun Button -->
    <button
      @click="rerunAnalysis"
      :disabled="isRerunning"
      class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <RefreshCw :class="['w-4 h-4 mr-2', { 'animate-spin': isRerunning }]" />
      {{ isRerunning ? 'Starting Rerun...' : 'Rerun Analysis' }}
    </button>

    <!-- Preview of rerun title -->
    <div class="text-xs text-gray-500">
      Next rerun will be titled: "{{ nextRerunTitle }}"
    </div>

    <!-- Success Message -->
    <div v-if="success" class="bg-green-50 border border-green-200 rounded-lg p-3">
      <div class="flex items-center text-sm text-green-700">
        <CheckCircle class="w-4 h-4 mr-2" />
        <span>{{ success }}</span>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-3">
      <div class="flex items-start text-sm text-red-700">
        <AlertTriangle class="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
        <div>
          <p class="font-medium">Rerun Failed</p>
          <p class="mt-1">{{ error }}</p>
          <a 
            v-if="error.includes('credits')"
            href="/dashboard/billing"
            class="inline-flex items-center mt-2 text-red-600 hover:text-red-800 font-medium"
          >
            Purchase More Credits
            <ArrowRight class="w-3 h-3 ml-1" />
          </a>
        </div>
      </div>
    </div>
  </div>
</template>