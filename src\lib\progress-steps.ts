/**
 * ConvertIQ Progress System - Redesigned for current_process field
 * This system uses a server-side current_process field that corresponds to frontend step indices
 * Color coding: Green = Active, Blue = Completed, Grey = Pending
 */

export interface ProgressStepDefinition {
  id: number;
  name: string;
  description: string;
  icon?: string;
  estimatedDuration?: number; // in seconds
  category?: string; // For grouping related steps
}

export interface ProgressStepStatus {
  stepNumber: number;
  stepName: string;
  description: string;
  status: 'pending' | 'active' | 'completed' | 'failed';
  isActive: boolean;
  isCompleted: boolean;
  isFailed: boolean;
  colorClass: string; // CSS classes for styling
}

export const PROGRESS_STEPS: ProgressStepDefinition[] = [
  {
    id: 0,
    name: "Initializing Analysis",
    description: "Setting up analysis environment and preparing data collection",
    icon: "Settings",
    estimatedDuration: 5,
    category: "setup"
  },
  {
    id: 1,
    name: "Generating Page Summary",
    description: "AI is analyzing your website's overall structure and content strategy",
    icon: "FileText",
    estimatedDuration: 15,
    category: "analysis"
  },
  {
    id: 2,
    name: "Analyzing Pros & Cons",
    description: "Identifying website strengths and weaknesses for conversion optimization",
    icon: "Scale",
    estimatedDuration: 20,
    category: "analysis"
  },
  {
    id: 3,
    name: "Lead Insights Analysis",
    description: "Evaluating lead generation potential and qualification strategies",
    icon: "Users",
    estimatedDuration: 18,
    category: "analysis"
  },
  {
    id: 4,
    name: "Performance Analysis",
    description: "Running Lighthouse performance tests and Core Web Vitals assessment",
    icon: "Zap",
    estimatedDuration: 25,
    category: "technical"
  },
  {
    id: 5,
    name: "SEO & Technical Analysis",
    description: "Analyzing technical SEO factors and search engine optimization opportunities",
    icon: "Search",
    estimatedDuration: 20,
    category: "technical"
  },
  {
    id: 6,
    name: "AI Performance Impact",
    description: "Generating AI-powered performance insights and optimization recommendations",
    icon: "TrendingUp",
    estimatedDuration: 22,
    category: "analysis"
  },
  {
    id: 7,
    name: "Chat Context Generation",
    description: "Preparing contextual AI chat interface with website-specific insights",
    icon: "MessageSquare",
    estimatedDuration: 15,
    category: "finalization"
  },
  {
    id: 8,
    name: "Finalizing Analysis",
    description: "Compiling results and preparing comprehensive conversion analysis report",
    icon: "CheckCircle",
    estimatedDuration: 10,
    category: "finalization"
  }
];

export const TOTAL_STEPS = PROGRESS_STEPS.length;

/**
 * Get progress step by ID (current_process value)
 */
export function getProgressStep(stepId: number): ProgressStepDefinition | undefined {
  return PROGRESS_STEPS.find(step => step.id === stepId);
}

/**
 * Generate step status with proper color coding
 * Green = Active, Blue = Completed, Grey = Pending
 */
export function getStepStatus(stepId: number, currentProcess: number, isCompleted: boolean): ProgressStepStatus {
  const step = getProgressStep(stepId);
  if (!step) {
    throw new Error(`Step with ID ${stepId} not found`);
  }

  const isActive = currentProcess === stepId && !isCompleted;
  const isStepCompleted = currentProcess > stepId || isCompleted;
  const isPending = currentProcess < stepId && !isCompleted;

  let status: 'pending' | 'active' | 'completed' | 'failed' = 'pending';
  let colorClass = '';

  if (isStepCompleted) {
    status = 'completed';
    colorClass = 'text-blue-600 bg-blue-50 border-blue-200'; // Blue for completed
  } else if (isActive) {
    status = 'active';
    colorClass = 'text-green-600 bg-green-50 border-green-200'; // Green for active
  } else {
    status = 'pending';
    colorClass = 'text-gray-400 bg-gray-50 border-gray-200'; // Grey for pending
  }

  return {
    stepNumber: stepId,
    stepName: step.name,
    description: step.description,
    status,
    isActive,
    isCompleted: isStepCompleted,
    isFailed: false,
    colorClass
  };
}

/**
 * Get progress percentage based on current_process field
 */
export function getProgressPercentage(currentProcess: number, isCompleted: boolean = false): number {
  if (isCompleted) return 100;
  if (currentProcess < 0) return 0;
  if (currentProcess >= TOTAL_STEPS - 1) return 95; // 95% when on last step, 100% when completed

  // Calculate percentage: process N means steps 0 to N are complete/in progress
  const progressRatio = (currentProcess + 1) / TOTAL_STEPS;
  return Math.min(95, Math.round(progressRatio * 100));
}

/**
 * Get estimated time remaining based on current_process field
 */
export function getEstimatedTimeRemaining(currentProcess: number): number {
  if (currentProcess < 0 || currentProcess >= TOTAL_STEPS - 1) return 0;

  let remainingTime = 0;
  for (let i = currentProcess + 1; i < TOTAL_STEPS; i++) {
    const step = getProgressStep(i);
    if (step?.estimatedDuration) {
      remainingTime += step.estimatedDuration;
    }
  }

  return remainingTime;
}

/**
 * Format time in seconds to human readable format
 */
export function formatTimeRemaining(seconds: number): string {
  if (seconds <= 0) return "Almost done";
  if (seconds < 60) return `${seconds}s remaining`;

  const minutes = Math.ceil(seconds / 60);
  if (minutes === 1) return "About 1 minute remaining";
  return `About ${minutes} minutes remaining`;
}

/**
 * Generate all step statuses for the progress display
 */
export function generateAllStepStatuses(currentProcess: number, isCompleted: boolean): ProgressStepStatus[] {
  return PROGRESS_STEPS.map(step => getStepStatus(step.id, currentProcess, isCompleted));
}

/**
 * Get step icon component name
 */
export function getStepIcon(stepId: number): string {
  const step = getProgressStep(stepId);
  return step?.icon || 'Clock';
}


