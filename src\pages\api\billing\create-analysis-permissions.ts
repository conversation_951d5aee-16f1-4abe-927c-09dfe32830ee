import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../../lib/supabase-client';
import { BillingService } from '../../../lib/billing-service';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const { userId, analysisId, packageType } = await request.json();

    if (!userId || !analysisId || !packageType) {
      return new Response(JSON.stringify({ error: 'Missing required parameters' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get package details
    const packages = await BillingService.getAvailablePackages();
    const selectedPackage = packages.find(p => p.id === packageType);

    if (!selectedPackage) {
      return new Response(JSON.stringify({ error: 'Invalid package type' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create analysis permissions record
    const { error: permissionsError } = await supabaseAdmin
      .from('analysis_permissions')
      .insert({
        analysis_id: analysisId,
        user_id: userId,
        package_type: packageType,
        has_summary: selectedPackage.features.has_summary,
        has_screenshot: selectedPackage.features.has_screenshot,
        has_score: selectedPackage.features.has_score,
        has_performance: selectedPackage.features.has_performance,
        has_suggestions: selectedPackage.features.has_suggestions,
        has_proscons: selectedPackage.features.has_proscons,
        has_leadinsights: selectedPackage.features.has_leadinsights,
        has_seo: selectedPackage.features.has_seo,
        chat_messages_remaining: selectedPackage.features.chat_messages,
        reruns_remaining: selectedPackage.features.reruns
      });

    if (permissionsError) throw permissionsError;

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error creating analysis permissions:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to create analysis permissions',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};