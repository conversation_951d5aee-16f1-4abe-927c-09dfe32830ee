import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../../lib/supabase-client';

export const prerender = false;

export const GET: APIRoute = async ({ request }) => {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user's active purchases
    const { data: purchases, error: purchasesError } = await supabaseAdmin
      .from('user_purchases')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (purchasesError) throw purchasesError;

    // Calculate total credits
    const totalAnalysisCredits = purchases?.reduce((sum, p) => sum + p.analysis_credits, 0) || 0;
    const totalChatMessages = purchases?.reduce((sum, p) => sum + p.chat_messages, 0) || 0;
    const totalReruns = purchases?.reduce((sum, p) => sum + p.reruns, 0) || 0;

    // Get usage statistics
    const { data: analyses, error: analysesError } = await supabaseAdmin
      .from('analyses')
      .select('id, created_at')
      .eq('user_id', userId);

    if (analysesError) throw analysesError;

    const { data: messages, error: messagesError } = await supabaseAdmin
      .from('messages')
      .select('id, created_at')
      .eq('user_id', userId)
      .eq('role', 'user');

    if (messagesError) throw messagesError;

    const balance = {
      analysis_credits: totalAnalysisCredits,
      chat_messages: totalChatMessages,
      reruns: totalReruns,
      purchased_packages: purchases || [],
      usage_stats: {
        total_analyses: analyses?.length || 0,
        total_chat_messages: messages?.length || 0,
        this_month_analyses: analyses?.filter(a => 
          new Date(a.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        ).length || 0
      }
    };

    return new Response(JSON.stringify(balance), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error fetching user balance:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to fetch user balance',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};