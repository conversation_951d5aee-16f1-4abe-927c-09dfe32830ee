/*
  # Add Billing and Payment Tables

  1. New Tables
    - `products` - Available analysis packages and add-ons
    - `user_purchases` - User purchase history and credits
    - `analysis_permissions` - Per-analysis feature permissions
    - `user_addons` - Additional features purchased per analysis

  2. Security
    - Enable RLS on all new tables
    - Add policies for user data access
    - Ensure users can only access their own billing data

  3. Indexes
    - Add indexes for efficient querying
    - Optimize for billing dashboard performance
*/

-- Products table for available packages
CREATE TABLE IF NOT EXISTS products (
  id text PRIMARY KEY,
  name text NOT NULL,
  description text NOT NULL,
  price numeric NOT NULL CHECK (price >= 0),
  currency text NOT NULL DEFAULT 'usd',
  type text NOT NULL CHECK (type IN ('package', 'addon')),
  features jsonb NOT NULL DEFAULT '{}',
  stripe_price_id text NOT NULL,
  is_active boolean DEFAULT true,
  display_order integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User purchases and credits
CREATE TABLE IF NOT EXISTS user_purchases (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id),
  product_id text NOT NULL,
  price_id text NOT NULL,
  package_type text,
  category text NOT NULL,
  analysis_credits integer DEFAULT 0,
  chat_messages integer DEFAULT 0,
  reruns integer DEFAULT 0,
  stripe_payment_intent_id text,
  amount_paid numeric NOT NULL,
  currency text DEFAULT 'usd',
  purchase_date timestamptz DEFAULT now(),
  is_active boolean DEFAULT true,
  expires_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Analysis permissions per user per analysis
CREATE TABLE IF NOT EXISTS analysis_permissions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE REFERENCES analyses(id),
  user_id uuid NOT NULL REFERENCES profiles(id),
  package_type text NOT NULL,
  has_summary boolean DEFAULT true,
  has_screenshot boolean DEFAULT true,
  has_score boolean DEFAULT true,
  has_performance boolean DEFAULT false,
  has_suggestions boolean DEFAULT false,
  has_proscons boolean DEFAULT false,
  has_leadinsights boolean DEFAULT false,
  has_seo boolean DEFAULT false,
  chat_messages_remaining integer DEFAULT 0,
  reruns_remaining integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User add-ons for specific analyses
CREATE TABLE IF NOT EXISTS user_addons (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id),
  analysis_id uuid REFERENCES analyses(id),
  addon_type text NOT NULL,
  purchase_id uuid NOT NULL REFERENCES user_purchases(id),
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_addons ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Products are viewable by everyone"
  ON products FOR SELECT
  TO authenticated
  USING (is_active = true);

CREATE POLICY "Users can view their own purchases"
  ON user_purchases FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own analysis permissions"
  ON analysis_permissions FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own addons"
  ON user_addons FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Insert default products
INSERT INTO products (id, name, description, price, type, features, stripe_price_id) VALUES
('basic', 'Basic Analysis', 'Essential insights for your landing page', 9.99, 'package', 
 '{"has_summary": true, "has_screenshot": true, "has_score": true, "has_performance": false, "has_suggestions": false, "has_proscons": false, "has_leadinsights": false, "has_seo": false, "chat_messages": 5, "reruns": 1}', 
 'price_basic_analysis'),
('professional', 'Professional Analysis', 'Comprehensive analysis with AI insights', 29.99, 'package',
 '{"has_summary": true, "has_screenshot": true, "has_score": true, "has_performance": true, "has_suggestions": true, "has_proscons": true, "has_leadinsights": false, "has_seo": true, "chat_messages": 20, "reruns": 3}',
 'price_professional_analysis'),
('premium', 'Premium Analysis', 'Complete analysis suite with lead insights', 49.99, 'package',
 '{"has_summary": true, "has_screenshot": true, "has_score": true, "has_performance": true, "has_suggestions": true, "has_proscons": true, "has_leadinsights": true, "has_seo": true, "chat_messages": 50, "reruns": 5}',
 'price_premium_analysis'),
('chat_addon', 'Extra Chat Messages', 'Add 25 additional chat messages', 4.99, 'addon',
 '{"chat_messages": 25}',
 'price_chat_addon'),
('rerun_addon', 'Extra Reruns', 'Add 3 additional analysis reruns', 7.99, 'addon',
 '{"reruns": 3}',
 'price_rerun_addon')
ON CONFLICT (id) DO NOTHING;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_purchases_user_id ON user_purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_user_purchases_active ON user_purchases(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_analysis_permissions_analysis_id ON analysis_permissions(analysis_id);
CREATE INDEX IF NOT EXISTS idx_analysis_permissions_user_id ON analysis_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_addons_user_id ON user_addons(user_id);