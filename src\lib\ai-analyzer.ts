import { log } from './logger';

const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

if (!OPENROUTER_API_KEY) {
  throw new Error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
}

const activeModel = 'qwen/qwen-2.5-72b-instruct:free';

export interface AIAnalysisResult {
  pageSummary: string;
  leadInsights: {
    questions: string[];
    concerns: string[];
    businessQuestions: string[];
  };
  conversionScore: number; // 1-10
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  targetAudience: string;
}

export interface PerformanceSEOSummary {
  summary: string;
  keyFindings: string[];
  recommendations: Array<{
    title: string;
    description: string;
    impact: 'High' | 'Medium' | 'Low';
    effort: 'High' | 'Medium' | 'Low';
    category: string;
  }>;
  businessImpact: string;
}

export async function generatePageSummary(htmlContent: string, url: string): Promise<string> {
  try {
    log.info(`Generating page summary for ${url}`);

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are an expert in website analysis and user experience. Analyze the provided webpage content and create a concise summary of what users would take away from visiting this page."
          },
          {
            role: "user",
            content: `Analyze this webpage from ${url} and provide a clear, concise summary of what visitors would take away from this page. Focus on the main value proposition, key messages, and overall user experience.

HTML Content (truncated):
${htmlContent.substring(0, 8000)}

Provide a 2-3 sentence summary that captures the essence of what users would learn or gain from visiting this page.`
          }
        ],
        max_tokens: 300,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const summary = data.choices[0]?.message?.content || 'Unable to generate page summary';
    log.info(`Page summary generated for ${url}`);
    return summary;

  } catch (error) {
    log.error(`Error generating page summary: ${error instanceof Error ? error.message : error}`);
    return 'This page provides information and services to visitors, though specific details could not be analyzed.';
  }
}

export async function generateLeadInsights(htmlContent: string, url: string): Promise<AIAnalysisResult['leadInsights']> {
  try {
    log.info(`Generating lead insights for ${url}`);

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are an expert in lead generation and sales psychology. Analyze the webpage content and identify potential questions, concerns, and objections that leads might have."
          },
          {
            role: "user",
            content: `Analyze this webpage from ${url} and identify potential questions and concerns that leads/prospects might have when visiting this page.

HTML Content (truncated):
${htmlContent.substring(0, 8000)}

Provide your analysis in JSON format:
{
  "questions": ["What specific questions might visitors have about the product/service?"],
  "concerns": ["What concerns or objections might they have?"],
  "businessQuestions": ["What questions might they have about the business itself?"]
}

Focus on realistic, specific questions that would help qualify leads and address their needs.`
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 800,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const insights = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`Lead insights generated for ${url}`);
    
    return {
      questions: insights.questions || ['How does this service work?', 'What are the pricing options?'],
      concerns: insights.concerns || ['Is this service reliable?', 'What if it doesn\'t work for my needs?'],
      businessQuestions: insights.businessQuestions || ['How long has this company been in business?', 'What kind of support do they provide?']
    };

  } catch (error) {
    log.error(`Error generating lead insights: ${error instanceof Error ? error.message : error}`);
    return {
      questions: ['How does this service work?', 'What are the pricing options?', 'Is there a free trial available?'],
      concerns: ['Is this service reliable?', 'What if it doesn\'t work for my needs?', 'How secure is my data?'],
      businessQuestions: ['How long has this company been in business?', 'What kind of support do they provide?', 'Who are their typical customers?']
    };
  }
}

export async function generateComprehensiveAnalysis(htmlContent: string, url: string): Promise<AIAnalysisResult> {
  try {
    log.info(`Generating comprehensive analysis for ${url}`);

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are an expert in conversion rate optimization, landing page analysis, and digital marketing. Analyze the webpage comprehensively and provide detailed insights."
          },
          {
            role: "user",
            content: `Analyze this webpage from ${url} comprehensively for conversion optimization.

HTML Content (truncated):
${htmlContent.substring(0, 10000)}

Provide your analysis in JSON format:
{
  "conversionScore": <number 1-10>,
  "strengths": ["List specific strengths of the page"],
  "weaknesses": ["List specific weaknesses that hurt conversions"],
  "recommendations": ["Specific actionable recommendations"],
  "targetAudience": "Detailed description of the target audience"
}

Focus on conversion optimization, user experience, and business impact. Be specific and actionable.`
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 1500,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const analysis = JSON.parse(data.choices[0]?.message?.content || '{}');
    
    // Generate additional insights
    const pageSummary = await generatePageSummary(htmlContent, url);
    const leadInsights = await generateLeadInsights(htmlContent, url);

    log.info(`Comprehensive analysis generated for ${url}`);

    return {
      pageSummary,
      leadInsights,
      conversionScore: analysis.conversionScore || 5,
      strengths: analysis.strengths || ['Page loads successfully'],
      weaknesses: analysis.weaknesses || ['Could benefit from optimization'],
      recommendations: analysis.recommendations || ['Consider A/B testing key elements'],
      targetAudience: analysis.targetAudience || 'General web users'
    };

  } catch (error) {
    log.error(`Error generating comprehensive analysis: ${error instanceof Error ? error.message : error}`);
    
    // Fallback analysis
    const pageSummary = await generatePageSummary(htmlContent, url).catch(() => 
      'This page provides information and services to visitors.'
    );
    const leadInsights = await generateLeadInsights(htmlContent, url).catch(() => ({
      questions: ['How does this service work?'],
      concerns: ['Is this service reliable?'],
      businessQuestions: ['How long has this company been in business?']
    }));

    return {
      pageSummary,
      leadInsights,
      conversionScore: 5,
      strengths: ['Page is accessible and loads properly'],
      weaknesses: ['Analysis could not be completed fully'],
      recommendations: ['Consider manual review of conversion elements'],
      targetAudience: 'General web users interested in the offered services'
    };
  }
}
