import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../../lib/supabase-client';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const { userId, creditType } = await request.json();

    if (!userId || !creditType) {
      return new Response(JSON.stringify({ error: 'Missing required parameters' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user's current balance
    const { data: purchases, error: fetchError } = await supabaseAdmin
      .from('user_purchases')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (fetchError) throw fetchError;

    if (!purchases || purchases.length === 0) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'No active purchases found' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Find a purchase with available credits for the requested type
    let targetPurchase = null;
    const creditField = creditType === 'analysis' ? 'analysis_credits' : 
                       creditType === 'chat' ? 'chat_messages' : 'reruns';

    for (const purchase of purchases) {
      if (purchase[creditField] > 0) {
        targetPurchase = purchase;
        break;
      }
    }

    if (!targetPurchase) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: `No ${creditType} credits available` 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Consume one credit
    const updateData = {
      [creditField]: targetPurchase[creditField] - 1,
      updated_at: new Date().toISOString()
    };

    const { error: updateError } = await supabaseAdmin
      .from('user_purchases')
      .update(updateData)
      .eq('id', targetPurchase.id);

    if (updateError) throw updateError;

    return new Response(JSON.stringify({ 
      success: true,
      remaining: updateData[creditField]
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error consuming credit:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to consume credit',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};