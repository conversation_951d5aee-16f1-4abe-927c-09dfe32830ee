import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { generateComprehensiveAnalysis } from '../../lib/ai-analyzer';
import { generateAIPerformanceSEOSummary } from '../../lib/ai-contextual-analyzer';
import { runLighthouseAnalysis, runSEOAnalysis, loadLighthouseDataFromDatabase } from '../../lib/lighthouse-analyzer';
import { loggedFetch, log } from '../../lib/logger';

export const prerender = false;

// Helper function to normalize impact levels to match database constraints
function normalizeImpactLevel(impact: string): 'High' | 'Medium' | 'Low' {
  const normalized = impact.toLowerCase();
  if (normalized.includes('high') || normalized.includes('critical') || normalized.includes('major')) {
    return 'High';
  }
  if (normalized.includes('low') || normalized.includes('minor') || normalized.includes('small')) {
    return 'Low';
  }
  return 'Medium';
}

interface Suggestion {
  analysis_id: string;
  title: string;
  category: string;
  description: string;
  impact_level: string;
  effort_level: string;
  priority: number;
}

interface UpdateData {
  pros: string;
  cons: string;
  target_audience: string;
  conversion_rate: number;
  score: number;
  suggestions_count: number;
  priority_issues_count: number;
  lighthouse_data: string;
  performance_score?: number;
  performance_grade?: string;
  lcp_score?: number;
  fid_score?: number;
  cls_score?: number;
  seo_score?: number;
  seo_data?: string;
}

export const POST: APIRoute = async ({ request }) => {
  try {
    const { url, analysisId } = await request.json();

    log.info(`Received request for comprehensive analysis for URL: ${url} with analysisId: ${analysisId}`);

    if (!url || !analysisId) {
      log.warn('Missing URL or analysisId in comprehensive analysis request');
      return new Response(
        JSON.stringify({ error: 'URL and analysisId are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Fetch website content
    log.debug(`Fetching website content for ${url}`);
    const baseUrl = new URL(request.url).origin;
    const scrapeResponse = await loggedFetch(`${baseUrl}/api/scrape-website?url=${encodeURIComponent(url)}`);

    if (!scrapeResponse.ok) {
        const errorText = await scrapeResponse.text();
        log.error(`Failed to scrape website: ${errorText}`);
        return new Response(JSON.stringify({ error: 'Failed to scrape website' }), { status: 500 });
    }

    const scrapedData = await scrapeResponse.json();
    const htmlContent = scrapedData.content || '';
    log.debug(`Successfully scraped website. Content length: ${htmlContent.length}`);

    // Run parallel analysis
    log.info('[Comprehensive] Starting parallel analysis...');
    const [aiAnalysis, seoData, performanceResponse] = await Promise.allSettled([
      generateComprehensiveAnalysis(htmlContent, url),
      runSEOAnalysis(url).catch(error => {
        log.warn(`[Comprehensive] SEO analysis failed: ${error.message}`);
        return null;
      }),
      // Fire-and-forget the performance analysis
      loggedFetch(`${baseUrl}/api/analyze-performance`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url, analysisId }),
      }).catch(error => {
        log.error(`[Comprehensive] Failed to trigger performance analysis: ${error.message}`);
        return null;
      })
    ]);

    log.info('[Comprehensive] Parallel analysis initiated.');

    const analysis = aiAnalysis.status === 'fulfilled' ? aiAnalysis.value : null;
    const seo = seoData.status === 'fulfilled' ? seoData.value : null;

    // The performance data will be handled asynchronously by the analyze-performance endpoint
    const performance = null;

    if (!analysis) {
      throw new Error('Failed to generate AI analysis');
    }

    // Generate performance & SEO summary
    const performanceSEOSummary = await generateAIPerformanceSEOSummary(analysis as any);

    // Prepare suggestions for database
    log.debug('Saving suggestions to database...');
    const suggestions: Suggestion[] = [];

    // Add AI recommendations
    analysis.recommendations.forEach((recommendation: string, index: number) => {
      suggestions.push({
        analysis_id: analysisId,
        title: recommendation.substring(0, 100),
        category: 'Conversion',
        description: recommendation,
        impact_level: index < 2 ? 'High' : index < 4 ? 'Medium' : 'Low',
        effort_level: 'Medium',
        priority: index + 1
      });
    });

    // Add performance & SEO summary recommendations
    if (performanceSEOSummary?.actionPlan) {
      performanceSEOSummary.actionPlan.forEach((rec: any, index: number) => {
        // Normalize impact level to match database constraints
        const normalizedImpactLevel = normalizeImpactLevel(rec.expectedImpact || 'Medium');

        suggestions.push({
          analysis_id: analysisId,
          title: rec.title,
          category: rec.phase,
          description: rec.description,
          impact_level: normalizedImpactLevel,
          effort_level: 'Medium', // This can be improved with more AI context
          priority: suggestions.length + index + 1
        });
      });
    }

    // Save suggestions to database
    if (suggestions.length > 0) {
      const { error: dbError } = await supabaseAdmin.from('suggestions').insert(suggestions);

      if (dbError) {
        log.error(`Error saving suggestions: ${dbError.message}`);
      } else {
        log.info('Suggestions saved to database.');
      }
    }

    // Save SEO issues if they exist
    if (seo?.issues && seo.issues.length > 0) {
      const seoIssues = seo.issues.map((issue) => ({
        analysis_id: analysisId,
        issue: issue.issue,
        recommendation: issue.recommendation,
        severity_score: issue.severity_score,
      }));

      const { error: seoError } = await supabaseAdmin.from('seo_issues').insert(seoIssues);
      if (seoError) {
        log.error(`Error saving SEO issues: ${seoError.message}`);
      }
    }

    // Update the analysis with comprehensive data
    const updateData: UpdateData = {
      pros: JSON.stringify(analysis.strengths),
      cons: JSON.stringify(analysis.weaknesses),
      target_audience: analysis.targetAudience,
      conversion_rate: analysis.conversionScore * 10, // Convert to percentage
      score: analysis.conversionScore,
      suggestions_count: suggestions.length,
      priority_issues_count: seo?.issues?.filter(issue => issue.severity_score >= 4).length || 0,
      lighthouse_data: JSON.stringify({
        pageSummary: analysis.pageSummary,
        leadInsights: analysis.leadInsights,
        performanceSEOSummary: performanceSEOSummary,
        scrapedData: scrapedData,
        performanceData: performance,
        seoData: seo
      })
    };

    // Add performance data if available
    // if (performance) {
    //   updateData.performance_score = performance.performanceScore;
    //   updateData.performance_grade = performance.grade;
    //   updateData.lcp_score = performance.coreWebVitals.lcp;
    //   updateData.fid_score = performance.coreWebVitals.fid;
    //   updateData.cls_score = performance.coreWebVitals.cls;
    // }

    // Add SEO data if available
    if (seo) {
      updateData.seo_score = seo.score;
      updateData.seo_data = JSON.stringify(seo.data);
    }

    const { error: updateError } = await supabaseAdmin.from('analyses').update(updateData).eq('id', analysisId);

    if (updateError) {
      log.error(`Error updating analysis: ${updateError.message}`);
    } else {
      log.info('Analysis updated in database.');
    }

    // Initialize sequential AI generation with progress tracking
    await supabaseAdmin.rpc('initialize_generation_progress', { p_analysis_id: analysisId });

    // Trigger sequential AI insights generation in background
    fetch(`${baseUrl}/api/generate-ai-insights-sequential`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ analysisId })
    }).catch(error => {
      log.warn(`Failed to trigger sequential AI insights generation: ${error.message}`);
    });

    return new Response(JSON.stringify({ 
      success: true,
      analysis,
      performanceData: performance,
      seoData: seo,
      performanceSEOSummary,
      suggestionsCount: suggestions.length
    }), { status: 200 });

  } catch (error) {
    log.error(`Error generating comprehensive analysis: ${error instanceof Error ? error.message : error}`);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), { status: 500 });
  }
};